CREATE TABLE game_level_reward
(
    id                     UUID PRIMARY KEY,
    created_at             TIMESTAMP NOT NULL,
    created_by             <PERSON><PERSON><PERSON>,
    updated_at             TIMESTAMP NOT NULL,
    updated_by             U<PERSON><PERSON>,
    deleted_at             TIMESTAMP,

    name                   TEXT      NOT NULL,
    game_level             TEXT      NOT NULL,
    type                   TEXT      NOT NULL,
    reward_photo_file_id   UUID,
    description            TEXT,
    reward_coupon_code     TEXT,
    reward_button_text     TEXT,
    reward_button_color    TEXT,
    reward_button_link_url TEXT,
    published              BOOLEAN   NOT NULL,
    listing_order          INT       NOT NULL,

    CONSTRAINT reward_photo FOREIGN KEY (reward_photo_file_id) REFERENCES app_file (id)
);

CREATE INDEX "game_level_reward__reward_photo_file_idx" ON game_level_reward (reward_photo_file_id);

-- new GameLevel enum entry - migrate all to it is the new default
UPDATE student
SET game_level = 'ZERO';
