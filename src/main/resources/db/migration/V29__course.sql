CREATE TABLE course
(
    id                            UUID PRIMARY KEY,
    created_at                    TIMESTAMPTZ NOT NULL,
    created_by                    <PERSON><PERSON><PERSON>,
    updated_at                    TIMESTAMPTZ NOT NULL,
    updated_by                    <PERSON><PERSON><PERSON>,
    deleted_at                    TIMESTAMPTZ,

    published                     BOOLEAN     NOT NULL,
    title                         TEXT        NOT NULL,
    course_category               TEXT        NOT NULL,
    visible_to_tiers              TEXT[]      NOT NULL,
    visible_to_discord_users      BOOLEAN     NOT NULL,
    description                   TEXT        NOT NULL,
    trader_id                     UUID        NOT NULL,
    total_duration_in_minutes     INT         NOT NULL,
    color                         TEXT        NOT NULL,
    thumbnail_url                 TEXT        NOT NULL,
    trailer_url                   TEXT        NOT NULL,
    intro_picture_desktop_file_id UUID,
    intro_picture_mobile_file_id  UUID,

    CONSTRAINT trader FOREIGN KEY (trader_id) REFERENCES trader (id),
    CONSTRAINT intro_picture_desktop FOREIGN KEY (intro_picture_desktop_file_id) REFERENCES app_file (id),
    CONSTRAINT intro_picture_mobile FOREIGN KEY (intro_picture_mobile_file_id) REFERENCES app_file (id)
);

CREATE INDEX "course__trader_idx" ON course (trader_id);
CREATE INDEX "course__intro_picture_desktop_file_idx" ON course (intro_picture_desktop_file_id);
CREATE INDEX "course__intro_picture_mobile_file_idx" ON course (intro_picture_mobile_file_id);

