package com.cleevio.fundedmind.adapter.`in`.rest.request

import com.cleevio.fundedmind.application.module.user.student.command.StudentPatchesPrivacyCommand
import com.cleevio.fundedmind.domain.common.constant.LevelVisibility
import com.cleevio.fundedmind.domain.common.constant.NetworkingVisibility
import java.util.UUID

data class StudentPatchesPrivacyRequest(
    val networkingVisibility: NetworkingVisibility?,
    val levelVisibility: LevelVisibility?,
) {
    fun toCommand(studentId: UUID) = StudentPatchesPrivacyCommand(
        studentId = studentId,
        networkingVisibility = networkingVisibility,
        levelVisibility = levelVisibility,
    )
}
