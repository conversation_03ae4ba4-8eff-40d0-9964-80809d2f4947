package com.cleevio.fundedmind.adapter.`in`.rest

import com.cleevio.fundedmind.adapter.`in`.ApiVersion
import com.cleevio.fundedmind.adapter.`in`.SwaggerBearerToken
import com.cleevio.fundedmind.application.common.command.CommandBus
import com.cleevio.fundedmind.application.common.query.QueryBus
import com.cleevio.fundedmind.application.module.studentgamelevelhistory.command.StudentMarksGameLevelHistoryShownCommand
import com.cleevio.fundedmind.application.module.studentgamelevelhistory.query.StudentGetsGameLevelHistoryQuery
import com.cleevio.fundedmind.domain.user.appuser.constant.UserRole
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.tags.Tag
import jakarta.annotation.security.RolesAllowed
import org.springframework.http.HttpStatus
import org.springframework.security.core.annotation.AuthenticationPrincipal
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PatchMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.ResponseStatus
import org.springframework.web.bind.annotation.RestController
import java.util.UUID

@Tag(name = "Student Game Level History [My Profile]")
@RestController
@SwaggerBearerToken
@RequestMapping("/game-level-history/me")
class StudentGameLevelHistoryController(
    private val commandBus: CommandBus,
    private val queryBus: QueryBus,
) {

    @Operation(
        description = """
            Student marks their game level history entry as shown.
            If entry is already shown, nothing happens.
            
            403 - student game level history entry does not belong to authenticated student.
            404 - student game level history entry not found.
        """,
    )
    @RolesAllowed(UserRole.STUDENT_ROLE)
    @PatchMapping("/{id}/mark-shown", produces = [ApiVersion.VERSION_1_JSON])
    @ResponseStatus(HttpStatus.NO_CONTENT)
    fun studentMarksGameLevelHistoryShown(
        @AuthenticationPrincipal studentId: UUID,
        @PathVariable id: UUID,
    ): Unit = commandBus(
        StudentMarksGameLevelHistoryShownCommand(
            studentGameLevelHistoryId = id,
            studentId = studentId,
        ),
    )

    @Operation(
        description = """
            Student gets their game level history.
        """,
    )
    @RolesAllowed(UserRole.STUDENT_ROLE)
    @GetMapping(produces = [ApiVersion.VERSION_1_JSON])
    @ResponseStatus(HttpStatus.OK)
    fun studentGetsGameLevelHistory(
        @AuthenticationPrincipal studentId: UUID,
    ): StudentGetsGameLevelHistoryQuery.Result = queryBus(StudentGetsGameLevelHistoryQuery(studentId = studentId))
}
