package com.cleevio.fundedmind.adapter.`in`.rest

import com.cleevio.fundedmind.adapter.`in`.ApiVersion
import com.cleevio.fundedmind.adapter.`in`.SwaggerBearerToken
import com.cleevio.fundedmind.adapter.`in`.rest.request.ApplyVerificationCodeRequest
import com.cleevio.fundedmind.adapter.`in`.rest.request.SendVerificationCodeRequest
import com.cleevio.fundedmind.application.common.command.CommandBus
import com.cleevio.fundedmind.application.common.query.QueryBus
import com.cleevio.fundedmind.application.module.user.appuser.command.ApplyVerificationCodeCommand
import com.cleevio.fundedmind.application.module.user.appuser.command.InitiateCheckoutUpgradeToMasterclassCommand
import com.cleevio.fundedmind.application.module.user.appuser.command.InitiateCustomerPortalCommand
import com.cleevio.fundedmind.application.module.user.appuser.command.SyncUserEmailCommand
import com.cleevio.fundedmind.application.module.user.appuser.query.GetInvoicesQuery
import com.cleevio.fundedmind.application.module.user.appuser.query.GetUserQuery
import com.cleevio.fundedmind.domain.user.appuser.constant.UserRole
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.tags.Tag
import jakarta.annotation.security.RolesAllowed
import org.springframework.http.HttpStatus
import org.springframework.security.core.annotation.AuthenticationPrincipal
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.ResponseStatus
import org.springframework.web.bind.annotation.RestController
import java.util.UUID

@Tag(name = "User [My Profile]")
@RestController
@SwaggerBearerToken
@RequestMapping("/users/me")
class AppUserMeController(
    private val queryBus: QueryBus,
    private val commandBus: CommandBus,
) {

    @GetMapping(produces = [ApiVersion.VERSION_1_JSON])
    @ResponseStatus(HttpStatus.OK)
    fun getUser(@AuthenticationPrincipal userId: UUID): GetUserQuery.Result = queryBus(GetUserQuery(userId = userId))

    @Operation(
        description = """
            Sends one-time use verification code to the user's email.
            Verification code is saved in the database and is valid for pre-configured time.
            Creating new verification code will expire previous VALID one if such code exists.
            Expired verification codes are regularly being deleted.
            409 - if user already has a verification code with the same 'code'
        """,
    )
    @PostMapping("/verification-code", produces = [ApiVersion.VERSION_1_JSON])
    @ResponseStatus(HttpStatus.NO_CONTENT)
    fun sendVerificationCode(
        @AuthenticationPrincipal userId: UUID,
        @RequestBody request: SendVerificationCodeRequest,
    ): Unit = commandBus(request.toCommand(userId = userId))

    @Operation(
        description = """
            Uses verification code.
            Successful use of verification code will mark it as used and return 'VALID' status.
            404 - User does not have a provided verification code.
        """,
    )
    @PostMapping("/verification-code/apply", produces = [ApiVersion.VERSION_1_JSON])
    @ResponseStatus(HttpStatus.OK)
    fun applyVerificationCode(
        @AuthenticationPrincipal userId: UUID,
        @RequestBody request: ApplyVerificationCodeRequest,
    ): ApplyVerificationCodeCommand.Result = commandBus(request.toCommand(userId = userId))

    @Operation(
        description = """
            Initiates upgrade to masterclass tier by creating stripe checkout session.
            FE should use session secret to execute payment on FE.
            Once payment is confirmed by STRIPE, user will be upgraded to masterclass automatically by BE.
			400 - User (onboarding/student) does not have tier below MASTERCLASS.
		""",
    )
    @RolesAllowed(UserRole.STUDENT_ROLE)
    @PostMapping("/upgrade-to-masterclass", produces = [ApiVersion.VERSION_1_JSON])
    @ResponseStatus(HttpStatus.CREATED)
    fun initiateCheckoutUpgradeToMasterclass(
        @AuthenticationPrincipal userId: UUID,
    ): InitiateCheckoutUpgradeToMasterclassCommand.Result = commandBus(
        InitiateCheckoutUpgradeToMasterclassCommand(userId = userId),
    )

    @Operation(
        description = """
            Sync user email in DB with Firebase email.
            Use after user changes email.
        """,
    )
    @PostMapping("/email/sync", produces = [ApiVersion.VERSION_1_JSON])
    @ResponseStatus(HttpStatus.NO_CONTENT)
    fun syncUserEmail(@AuthenticationPrincipal userId: UUID): Unit = commandBus(SyncUserEmailCommand(userId))

    @Operation(
        description = """
            Initiates customer portal session.
            Customer portal is a stripe-hosted page where customer can see their payments.
            404 - User is not tracked by stripe (e.g. user is an admin)
        """,
    )
    @PostMapping("/customer-portal", produces = [ApiVersion.VERSION_1_JSON])
    @ResponseStatus(HttpStatus.CREATED)
    fun initiateCustomerPortal(@AuthenticationPrincipal userId: UUID): InitiateCustomerPortalCommand.Result =
        commandBus(InitiateCustomerPortalCommand(userId = userId))

    @Operation(
        description = """
            User lists all invoices.
        """,
    )
    @GetMapping("/invoices", produces = [ApiVersion.VERSION_1_JSON])
    @ResponseStatus(HttpStatus.OK)
    fun getInvoices(@AuthenticationPrincipal userId: UUID): GetInvoicesQuery.Result =
        queryBus(GetInvoicesQuery(userId = userId))
}
