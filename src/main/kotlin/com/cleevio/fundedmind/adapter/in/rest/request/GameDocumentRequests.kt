package com.cleevio.fundedmind.adapter.`in`.rest.request

import com.cleevio.fundedmind.adapter.`in`.InfiniteScroll
import com.cleevio.fundedmind.application.module.gamedocument.command.CreateGameDocumentCommand
import com.cleevio.fundedmind.application.module.gamedocument.command.DenyGameDocumentCommand
import com.cleevio.fundedmind.application.module.gamedocument.command.StudentCreatesGameDocumentCommand
import com.cleevio.fundedmind.application.module.gamedocument.command.UpdateGameDocumentCommand
import com.cleevio.fundedmind.application.module.gamedocument.query.SearchGameDocumentsQuery
import com.cleevio.fundedmind.domain.common.constant.GameLevel
import com.cleevio.fundedmind.domain.gamedocument.constant.GameDocumentApprovalState
import com.cleevio.fundedmind.domain.gamedocument.constant.GameDocumentType
import com.cleevio.fundedmind.domain.gamedocument.constant.IssuingCompany
import java.math.BigDecimal
import java.time.LocalDate
import java.util.UUID

data class CreateGameDocumentRequest(
    val studentId: UUID,
    val type: GameDocumentType,
    val issuingCompany: IssuingCompany,
    val payoutAmount: BigDecimal?,
    val reachedLevel: GameLevel,
    val payoutDate: LocalDate,
    val truthScore: Int,
    val scoreMessage: String?,
) {
    fun toCommand() = CreateGameDocumentCommand(
        studentId = studentId,
        type = type,
        issuingCompany = issuingCompany,
        payoutAmount = payoutAmount,
        reachedLevel = reachedLevel,
        payoutDate = payoutDate,
        truthScore = truthScore,
        scoreMessage = scoreMessage,
    )
}

data class UpdateGameDocumentRequest(
    val type: GameDocumentType,
    val issuingCompany: IssuingCompany,
    val payoutAmount: BigDecimal?,
    val reachedLevel: GameLevel,
    val payoutDate: LocalDate,
    val truthScore: Int,
    val scoreMessage: String?,
) {
    fun toCommand(gameDocumentId: UUID) = UpdateGameDocumentCommand(
        gameDocumentId = gameDocumentId,
        type = type,
        issuingCompany = issuingCompany,
        payoutAmount = payoutAmount,
        reachedLevel = reachedLevel,
        payoutDate = payoutDate,
        truthScore = truthScore,
        scoreMessage = scoreMessage,
    )
}

data class DenyGameDocumentRequest(
    val denyMessage: String,
) {
    fun toCommand(gameDocumentId: UUID) = DenyGameDocumentCommand(
        gameDocumentId = gameDocumentId,
        denyMessage = denyMessage,
    )
}

data class SearchGameDocumentRequest(
    val studentId: UUID?,
    val state: GameDocumentApprovalState?,
) {
    fun toQuery(infiniteScroll: InfiniteScroll<UUID>) = SearchGameDocumentsQuery(
        infiniteScroll = infiniteScroll,
        filter = SearchGameDocumentsQuery.Filter(
            studentId = studentId,
            state = state,
        ),
    )
}

data class StudentCreatesGameDocumentRequest(
    val type: GameDocumentType,
    val issuingCompany: IssuingCompany,
    val payoutAmount: BigDecimal?,
    val payoutDate: LocalDate,
    val truthScore: Int,
    val scoreMessage: String?,
) {
    fun toCommand(studentId: UUID) = StudentCreatesGameDocumentCommand(
        studentId = studentId,
        type = type,
        issuingCompany = issuingCompany,
        payoutAmount = payoutAmount,
        payoutDate = payoutDate,
        truthScore = truthScore,
        scoreMessage = scoreMessage,
    )
}
