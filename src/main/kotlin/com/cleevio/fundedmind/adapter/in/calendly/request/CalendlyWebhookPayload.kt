package com.cleevio.fundedmind.adapter.`in`.calendly.request

import com.cleevio.fundedmind.application.common.type.CalendlyUserUri
import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonProperty
import java.time.Instant

/**
 * See documentation:
 * https://developer.calendly.com/api-docs/1da466e7fbc1b-get-sample-webhook-data#response-body
 */
@JsonIgnoreProperties(ignoreUnknown = true)
data class CalendlyWebhookPayload(
    @JsonProperty("event") val eventType: String,
    @JsonProperty("payload") val payload: InviteePayload,
) {
    @JsonIgnoreProperties(ignoreUnknown = true)
    data class InviteePayload(
        @JsonProperty("email") val email: String,
        @JsonProperty("first_name") val firstName: String?,
        @JsonProperty("last_name") val lastName: String?,
        @JsonProperty("name") val name: String,
        @JsonProperty("status") val status: String, // 'active' or 'canceled'
        @JsonProperty("event") val event: String,
        @JsonProperty("rescheduled") val rescheduled: Boolean,
        @JsonProperty("old_invitee") val oldInvitee: String?,
        @JsonProperty("cancellation") val cancellation: Cancellation?,
        @JsonProperty("scheduled_event") val scheduledEvent: ScheduledEvent, // data about meeting that was booked
    )

    @JsonIgnoreProperties(ignoreUnknown = true)
    data class Cancellation(
        @JsonProperty("canceled_by") val canceledBy: String, // name of the person who canceled
        @JsonProperty("reason") val reason: String?, // usually not null; cancelling via Google Calendar sends null
        @JsonProperty("canceler_type") val cancelerType: String, // 'host' or 'invitee'
    )

    @JsonIgnoreProperties(ignoreUnknown = true)
    data class ScheduledEvent(
        @JsonProperty("uri") val uri: String,
        @JsonProperty("status") val status: String, // 'active' or 'canceled'
        @JsonProperty("start_time") val startTime: Instant,
        @JsonProperty("end_time") val endTime: Instant,
        @JsonProperty("location") val location: CalendlyBookingLocation,
        @JsonProperty("event_memberships") val eventMemberships: List<CalendlyEventMembership>,
    )

    @JsonIgnoreProperties(ignoreUnknown = true)
    data class CalendlyEventMembership(
        @JsonProperty("user") val user: CalendlyUserUri,
        @JsonProperty("user_email") val email: String,
    )

    @JsonIgnoreProperties(ignoreUnknown = true)
    data class CalendlyBookingLocation(
        @JsonProperty("join_url") val joinUrl: String?,
    )
}
