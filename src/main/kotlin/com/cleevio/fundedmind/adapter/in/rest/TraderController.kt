package com.cleevio.fundedmind.adapter.`in`.rest

import com.cleevio.fundedmind.adapter.`in`.ApiVersion
import com.cleevio.fundedmind.adapter.`in`.SwaggerBearerToken
import com.cleevio.fundedmind.adapter.`in`.rest.request.CreateNewTraderRequest
import com.cleevio.fundedmind.adapter.`in`.rest.request.ListAllTradersRequest
import com.cleevio.fundedmind.adapter.`in`.rest.request.ReorderTradersRequest
import com.cleevio.fundedmind.adapter.`in`.rest.request.UpdateTraderRequest
import com.cleevio.fundedmind.application.common.command.CommandBus
import com.cleevio.fundedmind.application.common.command.IdResult
import com.cleevio.fundedmind.application.common.query.QueryBus
import com.cleevio.fundedmind.application.module.user.appuser.command.DisableTraderAccountCommand
import com.cleevio.fundedmind.application.module.user.appuser.command.EnableTraderAccountCommand
import com.cleevio.fundedmind.application.module.user.trader.query.GetTraderDetailQuery
import com.cleevio.fundedmind.application.module.user.trader.query.ListAllTradersQuery
import com.cleevio.fundedmind.application.module.user.trader.query.ListCalendlyTradersQuery
import com.cleevio.fundedmind.domain.user.appuser.constant.UserRole
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.tags.Tag
import jakarta.annotation.security.RolesAllowed
import org.springframework.http.HttpStatus
import org.springframework.security.core.annotation.AuthenticationPrincipal
import org.springframework.web.bind.annotation.DeleteMapping
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.PutMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.ResponseStatus
import org.springframework.web.bind.annotation.RestController
import java.util.UUID

@Tag(name = "Trader [Admin]")
@RestController
@SwaggerBearerToken
@RequestMapping("/traders")
class TraderController(
    private val commandBus: CommandBus,
    private val queryBus: QueryBus,
) {

    @RolesAllowed(UserRole.ADMIN_ROLE)
    @GetMapping("/{traderId}", produces = [ApiVersion.VERSION_1_JSON])
    @ResponseStatus(HttpStatus.OK)
    fun getTraderDetail(@PathVariable traderId: UUID): GetTraderDetailQuery.Result =
        queryBus(GetTraderDetailQuery(traderId = traderId))

    @Operation(
        description = """
            Admin creates new trader by promoting existing user to trader role.
            409 - provided calendly user uri is already taken
            422 - provided user to promote does not have role STUDENT
        """,
    )
    @RolesAllowed(UserRole.ADMIN_ROLE)
    @PostMapping(produces = [ApiVersion.VERSION_1_JSON])
    @ResponseStatus(HttpStatus.CREATED)
    fun createTrader(@RequestBody request: CreateNewTraderRequest): IdResult = commandBus(request.toCommand())

    @Operation(
        description = """
            Admin updates trader.
            409 - if calendly user uri is changed and the new one is already taken
        """,
    )
    @RolesAllowed(UserRole.ADMIN_ROLE)
    @PutMapping("/{traderId}", produces = [ApiVersion.VERSION_1_JSON])
    @ResponseStatus(HttpStatus.NO_CONTENT)
    fun updateTrader(
        @PathVariable traderId: UUID,
        @RequestBody request: UpdateTraderRequest,
    ): Unit = commandBus(request.toCommand(traderId = traderId))

    @Operation(
        description = """
            Admin lists all traders (Drag&Drop table).
            Search fields are: 'firstName', 'lastName'
	    """,
    )
    @RolesAllowed(UserRole.ADMIN_ROLE)
    @PostMapping("/list", produces = [ApiVersion.VERSION_1_JSON])
    @ResponseStatus(HttpStatus.OK)
    fun listAllTraders(@RequestBody request: ListAllTradersRequest): ListAllTradersQuery.Result =
        queryBus(request.toQuery())

    @Operation(
        description = """
            Admin changes display order of traders (Drag&Drop table).
            400 - display order is not positive or zero
            422 - payload does not contain all traders (traders might have been added or removed since last fetch)
	    """,
    )
    @RolesAllowed(UserRole.ADMIN_ROLE)
    @PutMapping("/reorder", produces = [ApiVersion.VERSION_1_JSON])
    @ResponseStatus(HttpStatus.NO_CONTENT)
    fun reorderTraders(@RequestBody request: ReorderTradersRequest): Unit = commandBus(request.toCommand())

    @Operation(
        description = """
            Admin disables account of a trader.
            Trader will not be able to login.
            Data of trader will remain in database.
            422 - trader is linked to a non-deleted saleable product
            422 - account does not belong to TRADER
            422 - account was not active
            422 - trader has unfinished mentoring
            422 - trader has not-yet-finished mentoring meetings
        """,
    )
    @RolesAllowed(UserRole.ADMIN_ROLE)
    @DeleteMapping("/{traderId}/disable", produces = [ApiVersion.VERSION_1_JSON])
    @ResponseStatus(HttpStatus.NO_CONTENT)
    fun disableTraderAccount(@PathVariable traderId: UUID): Unit =
        commandBus(DisableTraderAccountCommand(traderId = traderId))

    @Operation(
        description = """
            Admin enables account of a trader that was previously disabled.
            Trader will be able to login.
            422 - account does not belong to TRADER
            422 - account was not disabled
        """,
    )
    @RolesAllowed(UserRole.ADMIN_ROLE)
    @PostMapping("/{traderId}/enable", produces = [ApiVersion.VERSION_1_JSON])
    @ResponseStatus(HttpStatus.NO_CONTENT)
    fun enableTraderAccount(@PathVariable traderId: UUID): Unit =
        commandBus(EnableTraderAccountCommand(traderId = traderId))

    @Operation(
        description = """
            Admin lists all traders registered as Calendly users.
	    """,
    )
    @RolesAllowed(UserRole.ADMIN_ROLE)
    @GetMapping("/list/calendly", produces = [ApiVersion.VERSION_1_JSON])
    @ResponseStatus(HttpStatus.OK)
    fun listCalendlyTraders(@AuthenticationPrincipal userId: UUID): ListCalendlyTradersQuery.Result =
        queryBus(ListCalendlyTradersQuery(userId))
}
