package com.cleevio.fundedmind.adapter.`in`.rest.request

import com.cleevio.fundedmind.adapter.`in`.InfiniteScroll
import com.cleevio.fundedmind.application.common.command.Command
import com.cleevio.fundedmind.application.common.type.StripeProductId
import com.cleevio.fundedmind.application.module.product.command.CreateNewProductCommand
import com.cleevio.fundedmind.application.module.product.command.UpdateProductCommand
import com.cleevio.fundedmind.application.module.product.command.UpdateProductSaleabilityCommand
import com.cleevio.fundedmind.application.module.product.query.SearchProductQuery
import java.time.Instant
import java.util.UUID

data class CreateNewProductRequest(
    val name: String,
    val traderId: UUID,
    val stripeIdentifier: StripeProductId,
    val description: String,
    val altDescription: String,
    val sessionsCount: Int,
    val validityInDays: Int?,
) {
    fun toCommand() = CreateNewProductCommand(
        name = name,
        traderId = traderId,
        stripeIdentifier = stripeIdentifier,
        description = description,
        altDescription = altDescription,
        sessionsCount = sessionsCount,
        validityInDays = validityInDays,
    )
}

data class UpdateProductRequest(
    val name: String,
    val traderId: UUID,
    val stripeIdentifier: StripeProductId,
    val description: String,
    val altDescription: String,
    val sessionsCount: Int,
    val validityInDays: Int?,
) {
    fun toCommand(productId: UUID) = UpdateProductCommand(
        productId = productId,
        name = name,
        stripeIdentifier = stripeIdentifier,
        description = description,
        altDescription = altDescription,
        sessionsCount = sessionsCount,
        traderId = traderId,
        validityInDays = validityInDays,
    )
}

data class UpdateProductSaleabilityRequest(
    val saleable: Boolean,
) {
    fun toCommand(productId: UUID) = UpdateProductSaleabilityCommand(
        productId = productId,
        saleable = saleable,
    )
}

data class DeleteProductCommand(
    val productId: UUID,
    val now: Instant = Instant.now(),
) : Command<Unit>

data class SearchProductRequest(
    val searchString: String?,
) {
    fun toQuery(infiniteScroll: InfiniteScroll<UUID>) = SearchProductQuery(
        infiniteScroll = infiniteScroll,
        filter = SearchProductQuery.Filter(
            searchString = searchString,
        ),
    )
}
