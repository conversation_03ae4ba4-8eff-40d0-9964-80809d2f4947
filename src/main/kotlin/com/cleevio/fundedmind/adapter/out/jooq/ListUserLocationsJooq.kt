package com.cleevio.fundedmind.adapter.out.jooq

import com.cleevio.fundedmind.application.common.command.GeoLocationInput
import com.cleevio.fundedmind.application.common.command.GeoLocationValue
import com.cleevio.fundedmind.application.common.port.out.ListUserLocationsPort
import com.cleevio.fundedmind.jooq.tables.references.USER_LOCATION
import org.jooq.DSLContext
import org.jooq.impl.DSL
import org.springframework.stereotype.Component

@Component
class ListUserLocationsJooq(private val dslContext: DSLContext) : ListUserLocationsPort {

    override fun invoke(
        northEast: GeoLocationInput,
        southWest: GeoLocationInput,
    ): List<ListUserLocationsPort.ListUserLocationsResult> {
        // Create a bounding box using the northEast and southWest coordinates
        val envelope = DSL.field(
            "ST_MakeEnvelope(?, ?, ?, ?, 4326)",
            org.jooq.Geometry::class.java,
            southWest.longitude,
            southWest.latitude,
            northEast.longitude,
            northEast.latitude,
        )

        // Query user locations within the bounding box
        return dslContext
            .select(
                USER_LOCATION.ID,
                USER_LOCATION.OBFUSCATED_LATITUDE,
                USER_LOCATION.OBFUSCATED_LONGITUDE,
            )
            .from(USER_LOCATION)
            .where(
                DSL.condition("ST_Contains({0}, {1})", envelope, USER_LOCATION.OBFUSCATED_LOCATION),
            )
            .fetch()
            .map {
                ListUserLocationsPort.ListUserLocationsResult(
                    userLocationId = it[USER_LOCATION.ID]!!,
                    obfuscatedLocation = GeoLocationValue(
                        latitude = it[USER_LOCATION.OBFUSCATED_LATITUDE]!!,
                        longitude = it[USER_LOCATION.OBFUSCATED_LONGITUDE]!!,
                    ),
                )
            }
    }
}
