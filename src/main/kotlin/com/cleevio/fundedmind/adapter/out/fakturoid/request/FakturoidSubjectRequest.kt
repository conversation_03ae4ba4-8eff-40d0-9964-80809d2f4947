package com.cleevio.fundedmind.adapter.out.fakturoid.request

import com.fasterxml.jackson.annotation.JsonProperty

/**
 * Request model for creating a Fakturoid subject.
 * Based on https://www.fakturoid.cz/api/v3/#subject
 */
data class FakturoidSubjectRequest(
    // Required field
    val name: String,
    val type: String,

    @JsonProperty("custom_id")
    val customId: String? = null,

    val email: String? = null,
    val street: String? = null,
    val city: String? = null,
    val zip: String? = null,
    val country: String? = null,

    @JsonProperty("registration_no")
    val registrationNo: String? = null,

    @JsonProperty("vat_no")
    val vatNo: String? = null, // VAT-payer VAT number (DIČ, IČ DPH in Slovakia, typically starts with the country code)

    @JsonProperty("local_vat_no")
    val localVatNo: String? = null, // SK DIČ (only in Slovakia, does not start with country code)
)
