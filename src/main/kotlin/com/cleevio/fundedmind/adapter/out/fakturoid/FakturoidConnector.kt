package com.cleevio.fundedmind.adapter.out.fakturoid

import com.cleevio.fundedmind.adapter.out.BaseConnector
import com.cleevio.fundedmind.adapter.out.fakturoid.request.FakturoidInvoiceRequest
import com.cleevio.fundedmind.adapter.out.fakturoid.request.FakturoidPaymentRequest
import com.cleevio.fundedmind.adapter.out.fakturoid.request.FakturoidSubjectRequest
import com.cleevio.fundedmind.adapter.out.fakturoid.request.FakturoidTokenRequest
import com.cleevio.fundedmind.adapter.out.fakturoid.response.FakturoidInvoiceResponse
import com.cleevio.fundedmind.adapter.out.fakturoid.response.FakturoidSubjectResponse
import com.cleevio.fundedmind.adapter.out.fakturoid.response.FakturoidTokenResponse
import com.cleevio.fundedmind.application.common.type.StripeInvoiceId
import com.cleevio.fundedmind.application.common.util.EpochClockTicker
import com.cleevio.fundedmind.application.common.util.toBase64
import com.cleevio.fundedmind.infrastructure.properties.FakturoidProperties
import com.github.benmanes.caffeine.cache.Caffeine
import org.springframework.http.HttpHeaders
import org.springframework.stereotype.Component
import java.time.Clock
import java.util.concurrent.CompletableFuture
import java.util.concurrent.Executor
import kotlin.time.Duration.Companion.minutes
import kotlin.time.toJavaDuration

@Component
class FakturoidConnector(
    private val fakturoidProperties: FakturoidProperties,
) : BaseConnector(
    baseUrl = fakturoidProperties.baseUrl,
    restClientCustomizer = {
        it.defaultHeader(HttpHeaders.USER_AGENT, fakturoidProperties.userAgent)
    },
) {

    private val slug = fakturoidProperties.accountName

    /**
     * Search for a subject by email.
     *
     * @param email The email to search for
     * @return List of subjects matching the email
     */
    fun searchSubjectsByEmail(email: String): List<FakturoidSubjectResponse> {
        logger.debug("Searching for subject with email: $email")

        val response = restClient
            .get()
            .uriAndHeaders(
                path = "/accounts/$slug/subjects/search.json",
                queryParams = mapOf("query" to listOf(email)),
            )
            .header(HttpHeaders.AUTHORIZATION, "Bearer ${getAccessToken()}")
            .retrieveResponseWithErrorHandler<List<FakturoidSubjectResponse>>()

        logger.debug("Found ${response.size} subjects matching email: $email")

        return response
    }

    /**
     * Create a new subject.
     *
     * @param request The subject creation request
     * @return The created subject
     */
    fun createSubject(request: FakturoidSubjectRequest): FakturoidSubjectResponse {
        logger.debug("Creating new subject with name: ${request.name}")

        val response = restClient
            .post()
            .uriAndHeaders(path = "/accounts/$slug/subjects.json")
            .header(HttpHeaders.AUTHORIZATION, "Bearer ${getAccessToken()}")
            .header(HttpHeaders.CONTENT_TYPE, "application/json")
            .jsonBody(request)
            .retrieveResponseWithErrorHandler<FakturoidSubjectResponse>()

        logger.debug("Created subject with ID: ${response.id}")

        return response
    }

    /**
     * Create a new invoice.
     *
     * @param request The invoice creation request
     * @return The created invoice
     */
    fun createInvoice(request: FakturoidInvoiceRequest): FakturoidInvoiceResponse {
        logger.debug("Creating new invoice for subject ID: ${request.subjectId}")

        val response = restClient
            .post()
            .uriAndHeaders(path = "/accounts/$slug/invoices.json")
            .header(HttpHeaders.AUTHORIZATION, "Bearer ${getAccessToken()}")
            .header(HttpHeaders.CONTENT_TYPE, "application/json")
            .jsonBody(request)
            .retrieveResponseWithErrorHandler<FakturoidInvoiceResponse>()

        logger.debug("Created invoice with ID: ${response.id}")

        return response
    }

    /**
     * Search for an invoice by custom_id.
     */
    fun findInvoiceByCustomId(customId: StripeInvoiceId): FakturoidInvoiceResponse? {
        logger.debug("Searching for invoice with custom_id: $customId")

        val response = restClient
            .get()
            .uriAndHeaders(
                path = "/accounts/$slug/invoices.json",
                queryParams = mapOf("custom_id" to listOf(customId)),
            )
            .header(HttpHeaders.AUTHORIZATION, "Bearer ${getAccessToken()}")
            .retrieveResponseWithErrorHandler<List<FakturoidInvoiceResponse>>()

        logger.debug("Found ${response.size} invoices matching custom_id: $customId")

        return response.firstOrNull()
    }

    /**
     * Create a payment for an invoice.
     */
    fun createPaymentByInvoiceId(
        invoiceId: Long,
        request: FakturoidPaymentRequest,
    ) {
        logger.debug("Creating payment for invoice: '$invoiceId'")

        restClient
            .post()
            .uriAndHeaders(path = "/accounts/$slug/invoices/$invoiceId/payments.json")
            .header(HttpHeaders.AUTHORIZATION, "Bearer ${getAccessToken()}")
            .header(HttpHeaders.CONTENT_TYPE, "application/json")
            .jsonBody(request)
            .retrieveWithErrorHandler()

        logger.debug("Created payment for invoice: '$invoiceId'")
    }

    /**
     * Gets a valid access token, either from cache or by fetching a new one via API.
     *
     * https://www.fakturoid.cz/api/v3/authorization#authorization-code-flow
     */
    fun getAccessToken(): String = tokenCache.get("token").join()

    private fun fetchAccessToken(): String {
        val encodedToken = "${fakturoidProperties.clientId}:${fakturoidProperties.clientSecret}".toBase64()

        val response = restClient
            .post()
            .uriAndHeaders("/oauth/token")
            .header(HttpHeaders.AUTHORIZATION, "Basic $encodedToken")
            .header(HttpHeaders.CONTENT_TYPE, "application/json")
            .header(HttpHeaders.ACCEPT, "application/json")
            .jsonBody(FakturoidTokenRequest(grantType = "client_credentials"))
            .retrieveResponseWithErrorHandler<FakturoidTokenResponse>()

        return response.accessToken
    }

    /**
     * Caches Fakturoid OAuth token.
     * - New tokens are always fetched immediately
     * - Refresh after 1 hour: background revalidation while still serving requests
     * - Expire just before 2 hours: force revalidation if unused (Fakturoid token expires after 2 hours)
     */
    private val tokenCache = Caffeine
        .newBuilder()
        .refreshAfterWrite(60.minutes.toJavaDuration())
        .expireAfterWrite(110.minutes.toJavaDuration())
        .ticker(EpochClockTicker(Clock.systemDefaultZone()))
        .buildAsync { _: String, executor: Executor ->
            CompletableFuture.supplyAsync(
                { fetchAccessToken() },
                executor,
            )
        }
}
