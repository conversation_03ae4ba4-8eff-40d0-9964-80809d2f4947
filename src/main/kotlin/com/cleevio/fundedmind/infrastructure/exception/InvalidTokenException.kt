package com.cleevio.fundedmind.infrastructure.exception

import org.springframework.http.HttpStatus
import org.springframework.web.bind.annotation.ResponseStatus

@ResponseStatus(HttpStatus.UNAUTHORIZED)
class InvalidTokenException : FundedmindApiException(
    reason = ExtendedErrorReasonType.INVALID_TOKEN,
    message = "Invalid or missing Authorization header with proper Bearer token",
    shouldBeLoggedToSentry = false,
)

@ResponseStatus(HttpStatus.UNAUTHORIZED)
class InvalidApiKeyException(apiKey: String? = null) : FundedmindApiException(
    reason = ExtendedErrorReasonType.INVALID_API_KEY,
    message = if (apiKey == null) "Missing FundedMind API key header" else "Invalid FundedMind API key: '$apiKey'",
    shouldBeLoggedToSentry = false,
)

@ResponseStatus(HttpStatus.UNAUTHORIZED)
class InvalidHubspotSignatureException : FundedmindApiException(
    reason = ExtendedErrorReasonType.INVALID_SIGNATURE,
    message = "Invalid or missing 'X-HubSpot-Signature' header",
)

@ResponseStatus(HttpStatus.UNAUTHORIZED)
class InvalidCalendlySignatureException(message: String) : FundedmindApiException(
    reason = ExtendedErrorReasonType.INVALID_SIGNATURE,
    message = message,
)
