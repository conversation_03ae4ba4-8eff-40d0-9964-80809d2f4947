package com.cleevio.fundedmind.infrastructure.security.jwt

import com.cleevio.fundedmind.infrastructure.exception.InvalidTokenException
import jakarta.servlet.http.HttpServletRequest

private const val TOKEN_PREFIX = "Bearer "

internal fun getTokenFromRequest(request: HttpServletRequest): String = request
    .getHeader("Authorization")
    ?.let { if (it.startsWith(TOKEN_PREFIX)) it else null }
    ?.removePrefix(TOKEN_PREFIX)
    ?: throw InvalidTokenException()
