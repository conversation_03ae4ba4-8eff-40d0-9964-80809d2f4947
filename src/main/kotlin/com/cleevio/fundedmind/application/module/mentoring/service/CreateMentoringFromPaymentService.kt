package com.cleevio.fundedmind.application.module.mentoring.service

import com.cleevio.fundedmind.application.common.type.StripeProductId
import com.cleevio.fundedmind.application.common.type.StripeSessionId
import com.cleevio.fundedmind.application.common.util.toEndOfTheDay
import com.cleevio.fundedmind.application.module.mentoring.event.MentoringCreatedEvent
import com.cleevio.fundedmind.application.module.product.finder.ProductFinderService
import com.cleevio.fundedmind.application.module.user.student.finder.StudentFinderService
import com.cleevio.fundedmind.domain.mentoring.CreateMentoringService
import org.springframework.context.ApplicationEventPublisher
import org.springframework.stereotype.Service
import java.time.Instant
import java.time.LocalDate
import java.util.UUID

@Service
class CreateMentoringFromPaymentService(
    private val productFinderService: ProductFinderService,
    private val studentFinderService: StudentFinderService,
    private val createMentoringService: CreateMentoringService,
    private val applicationEventPublisher: ApplicationEventPublisher,
) {

    operator fun invoke(
        userId: UUID,
        productId: StripeProductId,
        sessionId: StripeSessionId,
    ) {
        val product = productFinderService.getByStripeIdentifier(productId)

        val studentId = userId
        studentFinderService.checkExistsById(studentId)

        val mentoring = createMentoringService.createMentoring(
            sessionIdentifier = sessionId,
            studentId = studentId,
            productId = product.id,
            productName = product.name,
            productAltDescription = product.altDescription,
            expiresAt = product.validityInDays?.let(::computeExpiration),
            sessionCount = product.sessionsCount,
        )

        applicationEventPublisher.publishEvent(MentoringCreatedEvent(mentoringId = mentoring.id))
    }

    private fun computeExpiration(validityInDays: Int): Instant {
        val today = LocalDate.now()
        val expirationDay = today.plusDays(validityInDays.toLong())
        return expirationDay.toEndOfTheDay()
    }
}
