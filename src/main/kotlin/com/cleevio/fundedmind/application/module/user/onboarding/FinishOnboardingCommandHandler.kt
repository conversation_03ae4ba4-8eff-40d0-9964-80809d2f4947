package com.cleevio.fundedmind.application.module.user.onboarding

import com.cleevio.fundedmind.application.common.command.CommandHandler
import com.cleevio.fundedmind.application.common.command.IdResult
import com.cleevio.fundedmind.application.common.constants.Locks
import com.cleevio.fundedmind.application.common.util.ifTrue
import com.cleevio.fundedmind.application.module.file.service.AppFileService
import com.cleevio.fundedmind.application.module.user.onboarding.command.FinishOnboardingCommand
import com.cleevio.fundedmind.application.module.user.onboarding.event.OnboardingFinishedEvent
import com.cleevio.fundedmind.application.module.user.onboarding.exception.OnboardingNotYetFinishedException
import com.cleevio.fundedmind.application.module.user.student.exception.StudentAlreadyOnboardedException
import com.cleevio.fundedmind.application.module.user.student.finder.OnboardingFinderService
import com.cleevio.fundedmind.application.module.user.student.finder.StudentFinderService
import com.cleevio.fundedmind.domain.common.constant.OnboardingTier
import com.cleevio.fundedmind.domain.common.constant.StudentTier
import com.cleevio.fundedmind.domain.file.constant.FileType
import com.cleevio.fundedmind.domain.user.onboarding.Onboarding
import com.cleevio.fundedmind.domain.user.student.CreateStudentService
import com.cleevio.fundedmind.infrastructure.config.logger
import com.cleevio.library.lockinghandler.service.Lock
import com.cleevio.library.lockinghandler.service.LockFieldParameter
import org.springframework.context.ApplicationEventPublisher
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional

@Component
class FinishOnboardingCommandHandler(
    private val studentFinderService: StudentFinderService,
    private val onboardingFinderService: OnboardingFinderService,
    private val createStudentService: CreateStudentService,
    private val appFileService: AppFileService,
    private val eventPublisher: ApplicationEventPublisher,
) : CommandHandler<IdResult, FinishOnboardingCommand> {

    override val command = FinishOnboardingCommand::class

    private val logger = logger()

    @Transactional
    @Lock(module = Locks.Student.MODULE, lockName = Locks.Student.CREATE)
    override fun handle(@LockFieldParameter("userId") command: FinishOnboardingCommand): IdResult {
        val onboardingId = command.userId

        val onboarding: Onboarding = onboardingFinderService.getById(onboardingId)

        val studentId = command.userId

        studentFinderService.existsById(studentId).ifTrue {
            if (studentFinderService.existsById(id = studentId)) {
                throw StudentAlreadyOnboardedException("Student with id: '$studentId' already onboarded.")
            }
        }

        val createdStudent = createStudentService.createStudent(
            id = studentId,
            profilePictureFileId = onboarding.profilePictureFileId
                ?.let { onboardingProfilePictureFileId ->
                    appFileService.copy(
                        sourceFileId = onboardingProfilePictureFileId,
                        destinationFileType = FileType.STUDENT_PROFILE_PICTURE,
                    )
                }
                ?.id,
            studentTier = when (onboarding.onboardingTier) {
                OnboardingTier.NO_TIER -> {
                    logger.warn("Onboarding: '$onboardingId' has no tier selected. Defaulting to BASECAMP.")
                    StudentTier.BASECAMP
                }

                OnboardingTier.BASECAMP -> StudentTier.BASECAMP

                OnboardingTier.MASTERCLASS -> StudentTier.MASTERCLASS
            },
            tierUpgradedAt = onboarding.tierUpgradedAt ?: run {
                logger.warn("Onboarding: '$onboardingId' has no tierUpgradedAt. Defaulting to updatedAt.")
                onboarding.updatedAt
            },

            firstName = onboarding.firstName ?: throw OnboardingNotYetFinishedException(
                "Onboarding ${onboarding.id} is not yet finished - missing first name.",
            ),
            lastName = onboarding.lastName ?: throw OnboardingNotYetFinishedException(
                "Onboarding ${onboarding.id} is not yet finished - missing last name.",
            ),
            phone = onboarding.phone ?: throw OnboardingNotYetFinishedException(
                "Onboarding ${onboarding.id} is not yet finished - missing phone.",
            ),
            biography = onboarding.biography,
            country = onboarding.country ?: throw OnboardingNotYetFinishedException(
                "Onboarding ${onboarding.id} is not yet finished - missing country.",
            ),
            questionnaire = onboarding.questionnaire ?: throw OnboardingNotYetFinishedException(
                "Onboarding ${onboarding.id} is not yet finished - missing questionnaire.",
            ),
            firstNameVocative = onboarding.firstNameVocative ?: throw OnboardingNotYetFinishedException(
                "Onboarding ${onboarding.id} is not yet finished - missing vocative first name.",
            ),
            lastNameVocative = onboarding.lastNameVocative ?: throw OnboardingNotYetFinishedException(
                "Onboarding ${onboarding.id} is not yet finished - missing vocative last name.",
            ),
            locationId = onboarding.locationId,
        )

        // location is now being used by Student entity - remove it from Onboarding
        onboarding.removeLocation()

        eventPublisher.publishEvent(
            OnboardingFinishedEvent(studentId = createdStudent.id),
        )

        return IdResult(createdStudent.id)
    }
}
