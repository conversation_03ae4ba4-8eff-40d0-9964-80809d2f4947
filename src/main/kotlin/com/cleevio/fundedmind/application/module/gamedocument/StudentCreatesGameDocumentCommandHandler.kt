package com.cleevio.fundedmind.application.module.gamedocument

import com.cleevio.fundedmind.application.common.command.CommandHandler
import com.cleevio.fundedmind.application.common.command.IdResult
import com.cleevio.fundedmind.application.common.constants.Locks
import com.cleevio.fundedmind.application.module.gamedocument.command.StudentCreatesGameDocumentCommand
import com.cleevio.fundedmind.application.module.gamedocument.event.GameDocumentCreatedEvent
import com.cleevio.fundedmind.application.module.user.student.finder.StudentFinderService
import com.cleevio.fundedmind.domain.gamedocument.CreateGameDocumentService
import com.cleevio.fundedmind.domain.gamedocument.constant.GameDocumentType
import com.cleevio.library.lockinghandler.service.Lock
import com.cleevio.library.lockinghandler.service.LockFieldParameter
import org.springframework.context.ApplicationEventPublisher
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional

@Component
class StudentCreatesGameDocumentCommandHandler(
    private val createGameDocumentService: CreateGameDocumentService,
    private val applicationEventPublisher: ApplicationEventPublisher,
    private val studentFinderService: StudentFinderService,
) : CommandHandler<IdResult, StudentCreatesGameDocumentCommand> {

    override val command = StudentCreatesGameDocumentCommand::class

    @Transactional
    @Lock(module = Locks.GameDocument.MODULE, lockName = Locks.GameDocument.CREATE)
    override fun handle(@LockFieldParameter("studentId") command: StudentCreatesGameDocumentCommand): IdResult {
        val student = studentFinderService.getById(command.studentId)

        val currentLevel = student.gameLevel

        when (command.type) {
            GameDocumentType.PAYOUT -> command.payoutAmount
            GameDocumentType.CERTIFICATE -> TODO()
            GameDocumentType.BACKTESTING -> TODO()
        }


        val gameDocument = createGameDocumentService.create(
            studentId = command.studentId,
            type = command.type,
            issuingCompany = command.issuingCompany,
            amount = command.payoutAmount,
            reachedLevel = command.reachedLevel,
            payoutDate = command.payoutDate,
            truthScore = command.truthScore,
            scoreMessage = command.scoreMessage,
        )

        applicationEventPublisher.publishEvent(GameDocumentCreatedEvent(gameDocumentId = gameDocument.id))

        return IdResult(id = gameDocument.id)
    }
}