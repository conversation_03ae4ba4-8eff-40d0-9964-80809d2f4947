package com.cleevio.fundedmind.application.module.meeting.command

import com.cleevio.fundedmind.application.common.command.Command
import com.cleevio.fundedmind.application.common.command.IdResult
import com.cleevio.fundedmind.application.common.validation.NotBlankAndLimited
import com.cleevio.fundedmind.application.common.validation.NullOrNotBlankAndLimited
import com.cleevio.fundedmind.domain.common.constant.Color
import com.cleevio.fundedmind.domain.common.constant.StudentTier
import java.time.Instant
import java.util.UUID

data class CreateNewMeetingCommand(
    @field:NotBlankAndLimited val name: String,
    val color: Color,
    val startAt: Instant,
    val finishAt: Instant,
    val traderIds: List<UUID>,
    @field:NullOrNotBlankAndLimited val description: String?,
    val invitedTiers: List<StudentTier>,
    val invitedDiscordUsers: Boolean,
    @field:NullOrNotBlankAndLimited val meetingUrl: String?,
    @field:NullOrNotBlankAndLimited val recordingUrl: String?,
) : Command<IdResult>

data class UpdateMeetingCommand(
    val meetingId: UUID,
    @field:NotBlankAndLimited val name: String,
    val color: Color,
    val startAt: Instant,
    val finishAt: Instant,
    val traderIds: List<UUID>,
    @field:NullOrNotBlankAndLimited val description: String?,
    val invitedTiers: List<StudentTier>,
    val invitedDiscordUsers: Boolean,
    @field:NullOrNotBlankAndLimited val meetingUrl: String?,
    @field:NullOrNotBlankAndLimited val recordingUrl: String?,
) : Command<Unit>

data class DeleteMeetingCommand(
    val meetingId: UUID,
) : Command<Unit>
