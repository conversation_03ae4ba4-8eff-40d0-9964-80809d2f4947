package com.cleevio.fundedmind.application.module.studentgamelevelhistory.query

import com.cleevio.fundedmind.application.common.query.Query
import com.cleevio.fundedmind.domain.common.constant.GameLevel
import io.swagger.v3.oas.annotations.media.Schema
import java.time.Instant
import java.util.UUID

data class StudentGetsGameLevelHistoryQuery(
    val studentId: UUID,
) : Query<StudentGetsGameLevelHistoryQuery.Result> {

    @Schema(name = "StudentGetsGameLevelHistoryResult")
    data class Result(
        val data: List<StudentGameLevelHistoryItem>,
    )

    @Schema(name = "StudentGameLevelHistoryItem")
    data class StudentGameLevelHistoryItem(
        val id: UUID,
        val level: GameLevel,
        val achievedAt: Instant,
        val shown: <PERSON>olean,
    )
}
