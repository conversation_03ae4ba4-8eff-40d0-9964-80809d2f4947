package com.cleevio.fundedmind.application.module.highlight

import com.cleevio.fundedmind.application.common.command.CommandHandler
import com.cleevio.fundedmind.application.common.constants.Locks
import com.cleevio.fundedmind.application.module.highlight.command.DeleteHighlightCommand
import com.cleevio.fundedmind.application.module.highlight.event.HighlightDeletedEvent
import com.cleevio.fundedmind.application.module.highlight.finder.HighlightFinderService
import com.cleevio.library.lockinghandler.service.Lock
import com.cleevio.library.lockinghandler.service.LockFieldParameter
import org.springframework.context.ApplicationEventPublisher
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional

@Component
class DeleteHighlightCommandHandler(
    private val highlightFinderService: HighlightFinderService,
    private val applicationEventPublisher: ApplicationEventPublisher,
) : CommandHandler<Unit, DeleteHighlightCommand> {

    override val command = DeleteHighlightCommand::class

    @Transactional
    @Lock(module = Locks.Highlight.MODULE, lockName = Locks.Highlight.UPDATE)
    override fun handle(@LockFieldParameter("highlightId") command: DeleteHighlightCommand) {
        highlightFinderService
            .getById(command.highlightId)
            .apply { softDelete() }
            .also { applicationEventPublisher.publishEvent(HighlightDeletedEvent(highlightId = it.id)) }
    }
}
