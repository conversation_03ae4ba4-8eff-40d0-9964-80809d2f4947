package com.cleevio.fundedmind.application.module.progress.event

import java.util.UUID

data class LessonFinishedEvent(
    val userId: UUID,
    val lessonId: UUID,
)

data class LessonFinishRevertedEvent(
    val userId: UUID,
    val lessonId: UUID,
)

data class CourseModuleFinishedEvent(
    val userId: UUID,
    val courseModuleId: UUID,
)

data class CourseModuleFinishRevertedEvent(
    val userId: UUID,
    val courseModuleId: UUID,
)

data class CourseFinishedEvent(
    val userId: UUID,
    val courseId: UUID,
)

data class CourseFinishRevertedEvent(
    val userId: UUID,
    val courseId: UUID,
)
