package com.cleevio.fundedmind.application.module.studentgamelevelhistory

import com.cleevio.fundedmind.application.common.query.QueryHandler
import com.cleevio.fundedmind.application.module.studentgamelevelhistory.finder.StudentGameLevelHistoryFinderService
import com.cleevio.fundedmind.application.module.studentgamelevelhistory.query.StudentGetsGameLevelHistoryQuery
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional

@Component
class StudentGetsGameLevelHistoryQueryHandler(
    private val studentGameLevelHistoryFinderService: StudentGameLevelHistoryFinderService,
) : QueryHandler<StudentGetsGameLevelHistoryQuery.Result, StudentGetsGameLevelHistoryQuery> {

    override val query = StudentGetsGameLevelHistoryQuery::class

    @Transactional(readOnly = true)
    override fun handle(query: StudentGetsGameLevelHistoryQuery): StudentGetsGameLevelHistoryQuery.Result =
        studentGameLevelHistoryFinderService
            .findAllByStudentId(query.studentId)
            .map { history ->
                StudentGetsGameLevelHistoryQuery.StudentGameLevelHistoryItem(
                    id = history.id,
                    level = history.gameLevel,
                    achievedAt = history.achievedAt,
                    shown = history.shown,
                )
            }
            .sortedByDescending { it.achievedAt }
            .let { StudentGetsGameLevelHistoryQuery.Result(data = it) }
}
