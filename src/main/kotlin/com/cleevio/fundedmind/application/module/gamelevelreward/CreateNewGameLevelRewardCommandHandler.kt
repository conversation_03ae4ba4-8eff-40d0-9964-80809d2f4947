package com.cleevio.fundedmind.application.module.gamelevelreward

import com.cleevio.fundedmind.application.common.command.CommandHandler
import com.cleevio.fundedmind.application.common.command.IdResult
import com.cleevio.fundedmind.application.common.constants.Locks
import com.cleevio.fundedmind.application.module.gamelevelreward.command.CreateNewGameLevelRewardCommand
import com.cleevio.fundedmind.application.module.gamelevelreward.event.GameLevelRewardCreatedEvent
import com.cleevio.fundedmind.application.module.gamelevelreward.finder.GameLevelRewardFinderService
import com.cleevio.fundedmind.domain.gamelevelreward.CreateGameLevelRewardService
import com.cleevio.fundedmind.domain.gamelevelreward.GameLevelReward
import com.cleevio.library.lockinghandler.service.Lock
import com.cleevio.library.lockinghandler.service.LockFieldParameter
import org.springframework.context.ApplicationEventPublisher
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional

@Component
class CreateNewGameLevelRewardCommandHandler(
    private val createGameLevelRewardService: CreateGameLevelRewardService,
    private val applicationEventPublisher: ApplicationEventPublisher,
    private val gameLevelRewardFinderService: GameLevelRewardFinderService,
) : CommandHandler<IdResult, CreateNewGameLevelRewardCommand> {

    override val command = CreateNewGameLevelRewardCommand::class

    @Transactional
    @Lock(module = Locks.GameLevelReward.MODULE, lockName = Locks.GameLevelReward.CREATE)
    override fun handle(@LockFieldParameter("name") command: CreateNewGameLevelRewardCommand): IdResult {
        val currentMaxOrder = gameLevelRewardFinderService.findMaxListingOrder() ?: 0

        val createdReward: GameLevelReward = createGameLevelRewardService.create(
            listingOrder = currentMaxOrder + 1,
            name = command.name,
            gameLevel = command.gameLevel,
            type = command.type,
            description = command.description,
            rewardCouponCode = command.rewardCouponCode,
            rewardButton = command.rewardButton?.toDomainButton(),
        )

        applicationEventPublisher.publishEvent(GameLevelRewardCreatedEvent(gameLevelRewardId = createdReward.id))

        return IdResult(createdReward.id)
    }
}
