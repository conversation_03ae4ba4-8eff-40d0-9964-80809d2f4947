package com.cleevio.fundedmind.application.module.studentgamelevelhistory

import com.cleevio.fundedmind.application.common.command.CommandHandler
import com.cleevio.fundedmind.application.common.constants.Locks
import com.cleevio.fundedmind.application.module.studentgamelevelhistory.command.StudentMarksGameLevelHistoryShownCommand
import com.cleevio.fundedmind.application.module.studentgamelevelhistory.finder.StudentGameLevelHistoryFinderService
import com.cleevio.library.lockinghandler.service.Lock
import com.cleevio.library.lockinghandler.service.LockFieldParameter
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

@Service
class StudentMarksGameLevelHistoryShownHandler(
    private val studentGameLevelHistoryFinderService: StudentGameLevelHistoryFinderService,
) : CommandHandler<Unit, StudentMarksGameLevelHistoryShownCommand> {
    override val command = StudentMarksGameLevelHistoryShownCommand::class

    @Transactional
    @Lock(module = Locks.Student.MODULE, lockName = Locks.Student.UPDATE)
    override fun handle(@LockFieldParameter("studentId") command: StudentMarksGameLevelHistoryShownCommand) {
        studentGameLevelHistoryFinderService
            .getById(command.studentGameLevelHistoryId)
            .apply {
                checkRelatedToStudent(command.studentId)
                markAsShown()
            }
    }
}
