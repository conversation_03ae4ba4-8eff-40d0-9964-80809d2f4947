package com.cleevio.fundedmind.application.module.gamedocument.command

import com.cleevio.fundedmind.application.common.command.Command
import com.cleevio.fundedmind.application.common.command.IdResult
import com.cleevio.fundedmind.application.common.validation.NotBlankAndLimited
import com.cleevio.fundedmind.application.common.validation.NullOrNotBlankAndLimited
import com.cleevio.fundedmind.domain.common.constant.GameLevel
import com.cleevio.fundedmind.domain.gamedocument.constant.GameDocumentType
import com.cleevio.fundedmind.domain.gamedocument.constant.IssuingCompany
import jakarta.validation.constraints.PositiveOrZero
import java.math.BigDecimal
import java.time.LocalDate
import java.util.UUID

data class CreateGameDocumentCommand(
    val studentId: UUID,
    val type: GameDocumentType,
    val issuingCompany: IssuingCompany,
    @field:PositiveOrZero val payoutAmount: BigDecimal?,
    val reachedLevel: GameLevel,
    val payoutDate: LocalDate,
    @field:PositiveOrZero val truthScore: Int,
    @field:NullOrNotBlankAndLimited val scoreMessage: String?,
) : Command<IdResult>

data class UpdateGameDocumentCommand(
    val gameDocumentId: UUID,
    val type: GameDocumentType,
    val issuingCompany: IssuingCompany,
    @field:PositiveOrZero val payoutAmount: BigDecimal?,
    val reachedLevel: GameLevel,
    val payoutDate: LocalDate,
    @field:PositiveOrZero val truthScore: Int,
    @field:NullOrNotBlankAndLimited val scoreMessage: String?,
) : Command<Unit>

data class ApproveGameDocumentCommand(
    val gameDocumentId: UUID,
) : Command<Unit>

data class DenyGameDocumentCommand(
    val gameDocumentId: UUID,
    @field:NotBlankAndLimited val denyMessage: String,
) : Command<Unit>

data class StudentCreatesGameDocumentCommand(
    val studentId: UUID,
    val type: GameDocumentType,
    val issuingCompany: IssuingCompany,
    @field:PositiveOrZero val payoutAmount: BigDecimal?,
    val payoutDate: LocalDate,
    @field:PositiveOrZero val truthScore: Int,
    @field:NullOrNotBlankAndLimited val scoreMessage: String?,
) : Command<IdResult>
