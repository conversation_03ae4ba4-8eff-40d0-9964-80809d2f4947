package com.cleevio.fundedmind.application.module.file.command

import com.cleevio.fundedmind.application.common.command.Command
import com.cleevio.fundedmind.application.common.command.DocumentResult
import com.cleevio.fundedmind.application.common.command.GameDocumentInput
import jakarta.validation.Valid
import java.util.UUID

data class StudentUploadsGameDocumentCommand(
    val studentId: UUID,
    @field:Valid val gameDocument: GameDocumentInput,
    val gameDocumentId: UUID,
) : Command<DocumentResult>