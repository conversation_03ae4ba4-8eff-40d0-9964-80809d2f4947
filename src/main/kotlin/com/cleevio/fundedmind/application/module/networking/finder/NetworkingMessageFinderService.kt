package com.cleevio.fundedmind.application.module.networking.finder

import com.cleevio.fundedmind.application.common.finder.BaseFinderService
import com.cleevio.fundedmind.domain.networking.NetworkingMessage
import com.cleevio.fundedmind.domain.networking.NetworkingMessageRepository
import com.cleevio.fundedmind.domain.networking.exception.NetworkingMessageNotFoundException
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

@Service
@Transactional(readOnly = true)
class NetworkingMessageFinderService(
    private val networkingMessageRepository: NetworkingMessageRepository,
) : BaseFinderService<NetworkingMessage>(networkingMessageRepository) {

    override fun errorBlock(message: String) = throw NetworkingMessageNotFoundException(message)

    override fun getEntityType() = NetworkingMessage::class
}
