package com.cleevio.fundedmind.application.module.product

import com.cleevio.fundedmind.application.common.command.CommandHandler
import com.cleevio.fundedmind.application.common.constants.Locks
import com.cleevio.fundedmind.application.common.util.ifFalse
import com.cleevio.fundedmind.application.module.product.command.UpdateProductSaleabilityCommand
import com.cleevio.fundedmind.application.module.product.event.ProductUpdatedEvent
import com.cleevio.fundedmind.application.module.product.finder.ProductFinderService
import com.cleevio.fundedmind.application.module.user.trader.exception.TraderAccountNotActiveException
import com.cleevio.fundedmind.application.module.user.trader.finder.TraderFinderService
import com.cleevio.library.lockinghandler.service.Lock
import com.cleevio.library.lockinghandler.service.LockFieldParameter
import org.springframework.context.ApplicationEventPublisher
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional
import java.util.UUID

@Component
class UpdateProductSaleabilityCommandHandler(
    private val productFinderService: ProductFinderService,
    private val traderFinderService: TraderFinderService,
    private val applicationEventPublisher: ApplicationEventPublisher,
) : CommandHandler<Unit, UpdateProductSaleabilityCommand> {

    override val command = UpdateProductSaleabilityCommand::class

    @Transactional
    @Lock(module = Locks.Product.MODULE, lockName = Locks.Product.UPDATE)
    override fun handle(@LockFieldParameter("productId") command: UpdateProductSaleabilityCommand) {
        productFinderService
            .getById(id = command.productId)
            .apply { checkTraderIsActive(this.traderId) }
            .apply {
                if (command.saleable) {
                    makeSaleable()
                } else {
                    makeUnsaleable()
                }
            }
            .also { product -> applicationEventPublisher.publishEvent(ProductUpdatedEvent(productId = product.id)) }
    }

    private fun checkTraderIsActive(traderId: UUID) {
        traderFinderService.existsByIdAndAccountActiveTrue(traderId = traderId).ifFalse {
            throw TraderAccountNotActiveException("Trader: '$traderId' account is not active.")
        }
    }
}
