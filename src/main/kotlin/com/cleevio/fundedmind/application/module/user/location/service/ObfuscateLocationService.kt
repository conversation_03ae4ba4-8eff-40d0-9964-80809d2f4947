package com.cleevio.fundedmind.application.module.user.location.service

import com.cleevio.fundedmind.application.common.command.GeoLocationValue
import com.cleevio.fundedmind.infrastructure.properties.UserLocationProperties
import org.springframework.stereotype.Service
import kotlin.math.PI
import kotlin.math.cos
import kotlin.math.sin
import kotlin.random.Random

@Service
class ObfuscateLocationService(
    private val userLocationProperties: UserLocationProperties,
) {

    /**
     * Obfuscates the provided coordinates by generating a random point
     * within a given radius of meters from the original location.
     */
    fun obfusacte(
        latitude: Double,
        longitude: Double,
        minRadius: Double = userLocationProperties.obfuscation.minRadiusMeters,
        maxRadius: Double = userLocationProperties.obfuscation.maxRadiusMeters,
    ): GeoLocationValue {
        require(minRadius >= 0.0 && maxRadius >= 0.0)
        require(minRadius <= maxRadius)
        require(latitude in -90.0..90.0 && longitude in -180.0..180.0)

        // Generate a random angle in radians (0 to 2π)
        val angle = Random.nextDouble() * 2 * PI

        // Generate a random radius between min and max
        val radius = minRadius + Random.nextDouble() * (maxRadius - minRadius)

        // Convert radius in meters to latitude degrees (approximate)
        // ~111,111 meters per degree of latitude
        val latOffset = radius * sin(angle) / 111111.0

        // Convert radius in meters to longitude degrees
        // Longitude degrees per meter varies with latitude
        val longOffset = radius * cos(angle) / (111111.0 * cos(Math.toRadians(latitude)))

        // Calculate the obfuscated coordinates
        val obfuscatedLatitude = latitude + latOffset
        val obfuscatedLongitude = longitude + longOffset

        return GeoLocationValue(
            latitude = obfuscatedLatitude,
            longitude = obfuscatedLongitude,
        )
    }
}
