package com.cleevio.fundedmind.application.module.mentoringmeeting.port.out

import com.cleevio.fundedmind.application.common.command.ImageResult
import com.cleevio.fundedmind.application.module.mentoringmeeting.query.GetMentoringMeetingInCalendarQuery
import com.cleevio.fundedmind.application.module.mentoringmeeting.query.ListMentoringMeetingsInCalendarQuery
import com.cleevio.fundedmind.application.module.mentoringmeeting.query.SearchMentoringMeetingsQuery
import com.cleevio.fundedmind.application.module.mentoringmeeting.query.StudentGetsTheirMentoringMeetingsQuery
import com.cleevio.fundedmind.application.module.mentoringmeeting.query.TraderGetsTheirMentoringMeetingsQuery
import com.cleevio.fundedmind.domain.common.constant.Color
import com.cleevio.fundedmind.domain.mentoringmeeting.constant.ModificationType
import java.time.Instant
import java.util.UUID

interface ListMentoringMeetingsInCalendarPort {

    fun traderListsTheirMentoringMeetings(
        traderId: UUID,
        filter: ListMentoringMeetingsInCalendarQuery.Filter,
    ): ListMentoringMeetingsInCalendarQuery.Result

    fun studentListsTheirMentoringMeetings(
        studentId: UUID,
        filter: ListMentoringMeetingsInCalendarQuery.Filter,
    ): ListMentoringMeetingsInCalendarQuery.Result

    fun listAllMentoringMeetings(
        filter: ListMentoringMeetingsInCalendarQuery.Filter,
    ): ListMentoringMeetingsInCalendarQuery.Result
}

interface GetMentoringMeetingInCalendarPort {
    fun getById(mentoringMeetingId: UUID): GetMentoringMeetingInCalendarQuery.Result
}

interface SearchMentoringMeetingsPort {
    fun search(filter: SearchMentoringMeetingsQuery.Filter): SearchMentoringMeetingsQuery.Result
}

interface StudentGetsTheirMentoringMeetingsPort {
    operator fun invoke(studentId: UUID): StudentGetsTheirMentoringMeetingsQuery.Result
}

interface TraderGetsTheirMentoringMeetingsPort {
    operator fun invoke(traderId: UUID): TraderGetsTheirMentoringMeetingsQuery.Result
}

interface UpcomingMentoringMeetingPort {
    fun findAllByStudentIdAndFinishAtAfter(
        studentId: UUID,
        instant: Instant,
    ): List<UpcomingMentoringMeeting>

    data class UpcomingMentoringMeeting(
        val id: UUID,
        val mentoringId: UUID,
        val color: Color,
        val startAt: Instant,
        val finishAt: Instant,
        val meetingUrl: String?,
        val recordingUrl: String?,
        val modificationType: ModificationType?,
        val coverPhoto: ImageResult?,
    )
}
