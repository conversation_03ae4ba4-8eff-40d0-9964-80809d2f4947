package com.cleevio.fundedmind.application.module.user.trader.query

import com.cleevio.fundedmind.application.common.command.ImageResult
import com.cleevio.fundedmind.application.common.command.UserLocationResult
import com.cleevio.fundedmind.application.common.query.Query
import com.cleevio.fundedmind.domain.common.constant.BadgeColor
import com.cleevio.fundedmind.domain.common.constant.Country
import com.cleevio.fundedmind.domain.common.constant.NetworkingVisibility
import io.swagger.v3.oas.annotations.media.Schema
import java.util.UUID

data class GetMyTraderProfileQuery(
    val traderId: UUID,
) : Query<GetMyTraderProfileQuery.Result> {

    @Schema(name = "GetMyTraderProfileResult")
    data class Result(
        val traderId: UUID,
        val firstName: String,
        val lastName: String,
        val biography: String?,
        val phone: String?,
        val country: Country,
        val badgeColor: BadgeColor,
        val commentControl: <PERSON><PERSON><PERSON>,
        val profilePicture: ImageResult?,
        val networkingVisibility: NetworkingVisibility,
        val location: UserLocationResult?,
    )
}
