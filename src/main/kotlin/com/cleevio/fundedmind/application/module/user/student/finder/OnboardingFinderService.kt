package com.cleevio.fundedmind.application.module.user.student.finder

import com.cleevio.fundedmind.application.common.finder.BaseFinderService
import com.cleevio.fundedmind.application.module.user.student.exception.StudentNotFoundException
import com.cleevio.fundedmind.domain.user.onboarding.Onboarding
import com.cleevio.fundedmind.domain.user.onboarding.OnboardingRepository
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

@Service
@Transactional(readOnly = true)
class OnboardingFinderService(
    private val onboardingRepository: OnboardingRepository,
) : BaseFinderService<Onboarding>(onboardingRepository) {

    override fun errorBlock(message: String) = throw StudentNotFoundException(message)

    override fun getEntityType() = Onboarding::class
}
