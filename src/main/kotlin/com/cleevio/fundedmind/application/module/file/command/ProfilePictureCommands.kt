package com.cleevio.fundedmind.application.module.file.command

import com.cleevio.fundedmind.application.common.command.Command
import com.cleevio.fundedmind.application.common.command.ImageInput
import com.cleevio.fundedmind.application.common.command.ImageResult
import com.cleevio.fundedmind.domain.file.constant.FileType
import jakarta.validation.Valid
import java.util.UUID

data class UserUploadsTheirProfilePictureCommand(
    val userId: UUID,
    val type: FileType,
    @field:Valid val image: ImageInput,
) : Command<ImageResult>

data class UserDeletesTheirProfilePictureCommand(
    val userId: UUID,
    val type: FileType,
) : Command<Unit>
