package com.cleevio.fundedmind.application.module.user.appuser.event.listener

import com.cleevio.fundedmind.application.module.user.appuser.service.SyncUserWithStripeCustomersService
import com.cleevio.fundedmind.application.module.user.student.event.StudentProfileUpdatedEvent
import io.sentry.spring.jakarta.tracing.SentryTransaction
import org.springframework.scheduling.annotation.Async
import org.springframework.stereotype.Component
import org.springframework.transaction.event.TransactionPhase
import org.springframework.transaction.event.TransactionalEventListener

@Component
class StudentUserEventListener(
    private val syncStudentUserWithStripeCustomersService: SyncUserWithStripeCustomersService,
) {
    @SentryTransaction(operation = "async.user.profile-updated")
    @Async
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT)
    fun handleStudentProfileUpdatedEvent(event: StudentProfileUpdatedEvent) {
        syncStudentUserWithStripeCustomersService.sync(event.studentId)
    }
}
