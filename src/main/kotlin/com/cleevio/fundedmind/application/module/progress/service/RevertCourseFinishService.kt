package com.cleevio.fundedmind.application.module.progress.service

import com.cleevio.fundedmind.application.module.progress.event.CourseFinishRevertedEvent
import com.cleevio.fundedmind.application.module.progress.finder.CourseProgressFinderService
import com.cleevio.fundedmind.domain.progress.CourseProgressRepository
import com.cleevio.fundedmind.infrastructure.config.logger
import org.springframework.context.ApplicationEventPublisher
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.util.UUID

/**
 * Service for reverting the finish status of a course.
 * @see FinishCourseService
 */
@Service
class RevertCourseFinishService(
    private val courseProgressFinderService: CourseProgressFinderService,
    private val courseProgressRepository: CourseProgressRepository,
    private val applicationEventPublisher: ApplicationEventPublisher,
) {
    private val logger = logger()

    @Transactional
    fun revertFinish(
        userId: UUID,
        courseId: UUID,
    ) {
        val courseProgress = courseProgressFinderService
            .findByCourseIdAndUserId(courseId, userId)
            ?: return // If there's no progress, there's nothing to revert

        logger.debug("Deleting course progress of course: '$courseId'.")
        courseProgressRepository.delete(courseProgress)

        applicationEventPublisher.publishEvent(
            CourseFinishRevertedEvent(
                userId = userId,
                courseId = courseId,
            ),
        )
    }
}
