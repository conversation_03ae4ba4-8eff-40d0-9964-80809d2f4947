package com.cleevio.fundedmind.application.module.mentoringmeeting

import com.cleevio.fundedmind.application.common.query.QueryHandler
import com.cleevio.fundedmind.application.module.mentoringmeeting.port.out.StudentGetsTheirMentoringMeetingsPort
import com.cleevio.fundedmind.application.module.mentoringmeeting.query.StudentGetsTheirMentoringMeetingsQuery
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional

@Component
class StudentGetsTheirMentoringMeetingsQueryHandler(
    private val studentGetsTheirMentoringMeetingsPort: StudentGetsTheirMentoringMeetingsPort,
) : QueryHandler<StudentGetsTheirMentoringMeetingsQuery.Result, StudentGetsTheirMentoringMeetingsQuery> {

    override val query = StudentGetsTheirMentoringMeetingsQuery::class

    @Transactional(readOnly = true)
    override fun handle(query: StudentGetsTheirMentoringMeetingsQuery): StudentGetsTheirMentoringMeetingsQuery.Result =
        studentGetsTheirMentoringMeetingsPort(studentId = query.studentId)
}
