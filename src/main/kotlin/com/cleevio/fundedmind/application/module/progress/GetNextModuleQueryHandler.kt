package com.cleevio.fundedmind.application.module.progress

import com.cleevio.fundedmind.application.common.query.QueryHandler
import com.cleevio.fundedmind.application.module.course.service.CheckUserAccessToMaterialService
import com.cleevio.fundedmind.application.module.progress.finder.CourseModuleProgressFinderService
import com.cleevio.fundedmind.application.module.progress.port.out.GetFirstUnfinishedLessonFromModulePort
import com.cleevio.fundedmind.application.module.progress.port.out.GetNextUnfinishedCourseModulePort
import com.cleevio.fundedmind.application.module.progress.query.GetNextModuleToWatchQuery
import com.cleevio.fundedmind.domain.coursemodule.exception.CourseModuleNotFinishedException
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional

@Component
class GetNextModuleQueryHandler(
    private val getNextUnfinishedCourseModulePort: GetNextUnfinishedCourseModulePort,
    private val getFirstUnfinishedLessonFromModulePort: GetFirstUnfinishedLessonFromModulePort,
    private val checkUserAccessToMaterialService: CheckUserAccessToMaterialService,
    private val courseModuleProgressFinderService: CourseModuleProgressFinderService,
) : QueryHandler<GetNextModuleToWatchQuery.Result, GetNextModuleToWatchQuery> {

    override val query = GetNextModuleToWatchQuery::class

    @Transactional(readOnly = true)
    override fun handle(query: GetNextModuleToWatchQuery): GetNextModuleToWatchQuery.Result {
        checkUserAccessToMaterialService.checkUserAccessToModule(
            userId = query.userId,
            courseModuleId = query.finishedModuleId,
        )

        val userProgress = courseModuleProgressFinderService.findByCourseModuleIdAndUserId(
            courseModuleId = query.finishedModuleId,
            userId = query.userId,
        )
        // check provided module is truly finished
        if (userProgress == null) {
            throw CourseModuleNotFinishedException("Course Module: '${query.finishedModuleId}' is not finished.")
        }

        val nextUnfinishedModule = getNextUnfinishedCourseModulePort(
            currentCourseModuleId = query.finishedModuleId,
            userId = query.userId,
        ) ?: return GetNextModuleToWatchQuery.Result(nextModule = null)

        val firstUnfinishedLessonFromModule = getFirstUnfinishedLessonFromModulePort(
            courseModuleId = nextUnfinishedModule.courseModuleId,
            userId = query.userId,
        ) ?: return GetNextModuleToWatchQuery.Result(
            // do not return next module if there is no lesson to watch
            nextModule = null,
        )

        return GetNextModuleToWatchQuery.Result(
            nextModule = GetNextModuleToWatchQuery.NextModule(
                moduleId = nextUnfinishedModule.courseModuleId,
                moduleTitle = nextUnfinishedModule.title,
                firstLessonId = firstUnfinishedLessonFromModule.lessonId,
            ),
        )
    }
}
