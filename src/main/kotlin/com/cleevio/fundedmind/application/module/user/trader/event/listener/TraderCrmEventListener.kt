package com.cleevio.fundedmind.application.module.user.trader.event.listener

import com.cleevio.fundedmind.application.module.crm.port.out.CrmUserRoleUpdatePort
import com.cleevio.fundedmind.application.module.user.appuser.finder.AppUserFinderService
import com.cleevio.fundedmind.application.module.user.trader.event.TraderCreatedEvent
import com.cleevio.fundedmind.application.module.user.trader.service.CreateCrmTraderPropertyService
import com.cleevio.fundedmind.domain.user.appuser.constant.UserRole
import io.sentry.spring.jakarta.tracing.SentryTransaction
import org.springframework.context.event.EventListener
import org.springframework.scheduling.annotation.Async
import org.springframework.stereotype.Component
import org.springframework.transaction.event.TransactionPhase
import org.springframework.transaction.event.TransactionalEventListener

@Component
class TraderCrmEventListener(
    private val crmUserRoleUpdatePort: CrmUserRoleUpdatePort,
    private val appUserFinderService: AppUserFinderService,
    private val createCrmTraderPropertyService: CreateCrmTraderPropertyService,
) {

    @EventListener
    fun handleNewTraderCreatedEvent(event: TraderCreatedEvent) {
        val appUser = appUserFinderService.getById(event.traderId)

        crmUserRoleUpdatePort.updateCrmUserRole(
            role = UserRole.TRADER,
            hubspotIdentifier = appUser.hubspotIdentifier,
        )
    }

    @SentryTransaction(operation = "async.crm.trader-created-property")
    @Async
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT)
    fun handleNewCreatedTraderAfterCommitEvent(event: TraderCreatedEvent) {
        createCrmTraderPropertyService(event.traderId)
    }
}
