package com.cleevio.fundedmind.application.module.payment

enum class PaymentType {
    UPGRADE_TO_MASTERCLASS, // stripe product id is known beforehand
    UPGRADE_TO_EXCLUSIVE, // stripe product id is known beforehand
    ONE_ON_ONE_MENTORING, // stripe product id is in Product entity - via traderId we know the mentor
    <PERSON><PERSON><PERSON><PERSON>_SUBSCRIPTION, // stripe product id is known beforehand

    UNKNOWN, // the system could not determine the payment type
}
