package com.cleevio.fundedmind.application.module.gamelevelreward

import com.cleevio.fundedmind.application.common.command.CommandHandler
import com.cleevio.fundedmind.application.common.constants.Locks
import com.cleevio.fundedmind.application.module.gamelevelreward.command.UpdateGameLevelRewardCommand
import com.cleevio.fundedmind.application.module.gamelevelreward.event.GameLevelRewardUpdatedEvent
import com.cleevio.fundedmind.application.module.gamelevelreward.finder.GameLevelRewardFinderService
import com.cleevio.library.lockinghandler.service.Lock
import com.cleevio.library.lockinghandler.service.LockFieldParameter
import org.springframework.context.ApplicationEventPublisher
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional

@Component
class UpdateGameLevelRewardCommandHandler(
    private val gameLevelRewardFinderService: GameLevelRewardFinderService,
    private val applicationEventPublisher: ApplicationEventPublisher,
) : CommandHandler<Unit, UpdateGameLevelRewardCommand> {

    override val command = UpdateGameLevelRewardCommand::class

    @Transactional
    @Lock(module = Locks.GameLevelReward.MODULE, lockName = Locks.GameLevelReward.UPDATE)
    override fun handle(@LockFieldParameter("gameLevelRewardId") command: UpdateGameLevelRewardCommand) {
        gameLevelRewardFinderService
            .getById(command.gameLevelRewardId)
            .apply {
                update(
                    name = command.name,
                    gameLevel = command.gameLevel,
                    type = command.type,
                    description = command.description,
                    rewardCouponCode = command.rewardCouponCode,
                    rewardButton = command.rewardButton?.toDomainButton(),
                )
            }
            .also {
                applicationEventPublisher.publishEvent(
                    GameLevelRewardUpdatedEvent(
                        gameLevelRewardId = command.gameLevelRewardId,
                    ),
                )
            }
    }
}
