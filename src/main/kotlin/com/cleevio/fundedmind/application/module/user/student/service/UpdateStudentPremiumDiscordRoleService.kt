package com.cleevio.fundedmind.application.module.user.student.service

import com.cleevio.fundedmind.application.common.port.out.DiscordUserRoleUpdatePort
import com.cleevio.fundedmind.application.module.user.student.finder.StudentDiscordFinderService
import org.springframework.stereotype.Service
import java.util.UUID

@Service
class UpdateStudentPremiumDiscordRoleService(
    private val discordUserRoleUpdatePort: DiscordUserRoleUpdatePort,
    private val studentDiscordFinderService: StudentDiscordFinderService,
) {

    fun grantPremiumRole(studentId: UUID) {
        studentDiscordFinderService
            .findByStudentIdNonDeleted(studentId)
            ?.run { discordUserRoleUpdatePort.grantDiscordUserRole(discordUserId = discordId) }
    }

    fun revokePremiumRole(studentId: UUID) {
        studentDiscordFinderService
            .findByStudentIdNonDeleted(studentId)
            ?.run { discordUserRoleUpdatePort.revokeDiscordUserRole(discordUserId = discordId) }
    }
}
