package com.cleevio.fundedmind.application.module.payment.command

import com.cleevio.fundedmind.application.common.command.Command
import com.cleevio.fundedmind.application.common.type.StripeCouponId
import com.cleevio.fundedmind.application.common.validation.NotBlankAndLimited
import io.swagger.v3.oas.annotations.media.Schema
import jakarta.validation.constraints.Min

data class GeneratePromotionCodesCommand(
    @field:NotBlankAndLimited val coupon: StripeCouponId,
    @field:Min(1) val quantity: Int,
    @field:Min(1) val maxRedemptions: Int,
) : Command<GeneratePromotionCodesCommand.Result> {

    @Schema(name = "GeneratePromotionCodesResult")
    data class Result(
        val promotionCodes: List<String>,
    )
}
