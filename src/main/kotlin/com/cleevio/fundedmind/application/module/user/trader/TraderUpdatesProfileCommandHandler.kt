package com.cleevio.fundedmind.application.module.user.trader

import com.cleevio.fundedmind.application.common.command.CommandHandler
import com.cleevio.fundedmind.application.common.constants.Locks
import com.cleevio.fundedmind.application.module.location.service.ProcessNewLocationService
import com.cleevio.fundedmind.application.module.user.trader.command.TraderUpdatesProfileCommand
import com.cleevio.fundedmind.application.module.user.trader.finder.TraderFinderService
import com.cleevio.library.lockinghandler.service.Lock
import com.cleevio.library.lockinghandler.service.LockFieldParameter
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

@Service
class TraderUpdatesProfileCommandHandler(
    private val traderFinderService: TraderFinderService,
    private val processNewLocationService: ProcessNewLocationService,
) : CommandHandler<Unit, TraderUpdatesProfileCommand> {
    override val command = TraderUpdatesProfileCommand::class

    @Transactional
    @Lock(module = Locks.Trader.MODULE, lockName = Locks.Trader.UPDATE)
    override fun handle(@LockFieldParameter("traderId") command: TraderUpdatesProfileCommand) {
        traderFinderService
            .getById(command.traderId)
            .apply {
                updateProfile(
                    firstName = command.firstName,
                    lastName = command.lastName,
                    phone = command.phone,
                    biography = command.biography,
                    country = command.country,
                    locationId = processNewLocationService.processNewLocation(
                        currentLocationId = this.locationId,
                        newLocationInput = command.location,
                    ),
                )
            }
    }
}
