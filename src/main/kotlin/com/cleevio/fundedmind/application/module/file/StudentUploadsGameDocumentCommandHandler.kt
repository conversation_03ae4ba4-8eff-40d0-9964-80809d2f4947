package com.cleevio.fundedmind.application.module.file

import com.cleevio.fundedmind.application.common.command.CommandHandler
import com.cleevio.fundedmind.application.common.command.DocumentResult
import com.cleevio.fundedmind.application.common.constants.Locks
import com.cleevio.fundedmind.application.module.file.command.StudentUploadsGameDocumentCommand
import com.cleevio.fundedmind.application.module.file.replacer.AppFileReplacerFactory
import com.cleevio.fundedmind.application.module.file.service.AppFileService
import com.cleevio.fundedmind.application.module.gamedocument.finder.GameDocumentFinderService
import com.cleevio.fundedmind.domain.file.constant.FileType
import com.cleevio.library.lockinghandler.service.Lock
import com.cleevio.library.lockinghandler.service.LockFieldParameter
import io.sentry.spring.jakarta.tracing.SentrySpan
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

@Service
class StudentUploadsGameDocumentCommandHandler(
    private val appFileService: AppFileService,
    private val appFileReplacerFactory: AppFileReplacerFactory,
    private val gameDocumentFinderService: GameDocumentFinderService,
) : CommandHandler<DocumentResult, StudentUploadsGameDocumentCommand> {

    override val command = StudentUploadsGameDocumentCommand::class

    @SentrySpan
    @Transactional
    @Lock(module = Locks.File.MODULE, lockName = Locks.File.UPDATE)
    override fun handle(@LockFieldParameter("studentId") command: StudentUploadsGameDocumentCommand): DocumentResult {
        val fileType = FileType.GAME_DOCUMENT

        gameDocumentFinderService
            .getById(command.gameDocumentId)
            .checkRelatedToStudent(command.studentId)

        val gameDocument = appFileService.uploadDocument(
            originalFile = command.gameDocument.originalFile,
            fileType = fileType,
        )

        appFileReplacerFactory
            .getFor(fileType = fileType)
            .replaceFile(entityReference = command.gameDocumentId, file = gameDocument)

        return DocumentResult.fromFile(gameDocument)
    }
}
