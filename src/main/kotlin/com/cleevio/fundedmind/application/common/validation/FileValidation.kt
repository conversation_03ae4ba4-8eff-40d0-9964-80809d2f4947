package com.cleevio.fundedmind.application.common.validation

import com.cleevio.fundedmind.infrastructure.config.logger
import jakarta.validation.Constraint
import jakarta.validation.ConstraintValidator
import jakarta.validation.ConstraintValidatorContext
import jakarta.validation.Payload
import org.springframework.util.StringUtils
import org.springframework.web.multipart.MultipartFile
import kotlin.reflect.KClass

@Constraint(validatedBy = [ValidFileValidator::class])
@Target(AnnotationTarget.FIELD, AnnotationTarget.TYPE, AnnotationTarget.ANNOTATION_CLASS)
@Retention(AnnotationRetention.RUNTIME)
annotation class ValidFile(
    val extensions: Array<String>,
    val message: String = "must be a valid file",
    val groups: Array<KClass<*>> = [],
    val payload: Array<KClass<out Payload>> = [],
)

@Constraint(validatedBy = [])
@Target(AnnotationTarget.FIELD, AnnotationTarget.TYPE)
@Retention(AnnotationRetention.RUNTIME)
@ValidFile(extensions = ["jpg", "jpeg", "png", "svg"], message = "must be a valid image")
annotation class ValidImage(
    val message: String = "must be a valid image",
    val groups: Array<KClass<*>> = [],
    val payload: Array<KClass<out Payload>> = [],
)

@Constraint(validatedBy = [])
@Target(AnnotationTarget.FIELD, AnnotationTarget.TYPE)
@Retention(AnnotationRetention.RUNTIME)
@ValidFile(
    extensions = ["pdf", "xlsx", "docx", "doc", "jpg", "jpeg", "png", "svg"],
    message = "must be a valid game document"
)
annotation class ValidGameDocument(
    val message: String = "must be a valid document",
    val groups: Array<KClass<*>> = [],
    val payload: Array<KClass<out Payload>> = [],
)

class ValidFileValidator : ConstraintValidator<ValidFile, MultipartFile> {
    private val logger = logger()

    private lateinit var validExtensions: List<String>

    override fun initialize(constraintAnnotation: ValidFile) {
        validExtensions = constraintAnnotation.extensions.toList()
        super.initialize(constraintAnnotation)
    }

    override fun isValid(
        value: MultipartFile?,
        context: ConstraintValidatorContext?,
    ): Boolean {
        if (value == null) return false
        val extension = StringUtils.getFilenameExtension(value.originalFilename) ?: return false

        if (validExtensions.any { validExtension -> validExtension.equals(extension, ignoreCase = true) }) {
            return true
        } else {
            val filename = StringUtils.getFilename(value.originalFilename)
            logger.info("Invalid file was attempted to be uploaded: '$filename'")
            return false
        }
    }
}
