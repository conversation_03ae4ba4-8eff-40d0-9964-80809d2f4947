package com.cleevio.fundedmind.application.common.util

import java.util.UUID

fun composeLessonUrl(
    urlPrefix: String,
    courseId: UUID,
    courseModuleId: UUID,
    lessonId: UUID,
    threadId: UUID?,
): String {
    val baseUrl = "$urlPrefix/$courseId/$courseModuleId/$lessonId"

    return if (threadId != null) {
        "$baseUrl?threadId=$threadId"
    } else {
        baseUrl
    }
}

fun composeNetworkingMessageUrl(
    urlPrefix: String,
    messageId: UUID,
): String = "$urlPrefix?messageId=$messageId"
