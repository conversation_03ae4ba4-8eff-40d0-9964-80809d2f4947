package com.cleevio.fundedmind.domain.course.exception

import com.cleevio.fundedmind.infrastructure.exception.ExtendedErrorReasonType
import com.cleevio.fundedmind.infrastructure.exception.FundedmindApiException
import org.springframework.http.HttpStatus
import org.springframework.web.bind.annotation.ResponseStatus

@ResponseStatus(HttpStatus.NOT_FOUND)
class CourseNotFoundException(message: String) : FundedmindApiException(
    reason = ExtendedErrorReasonType.COURSE_NOT_FOUND,
    message = message,
)

@ResponseStatus(HttpStatus.UNPROCESSABLE_ENTITY)
class CourseMissingPictureException(message: String) : FundedmindApiException(
    reason = ExtendedErrorReasonType.COURSE_PICTURE_MISSING,
    message = message,
    shouldBeLoggedToSentry = false,
)

@ResponseStatus(HttpStatus.BAD_REQUEST)
class CourseOrderCannotBeNegativeException(message: String) : FundedmindApiException(
    reason = ExtendedErrorReasonType.COURSE_ORDER_CANNOT_BE_NEGATIVE,
    message = message,
)

@ResponseStatus(HttpStatus.BAD_REQUEST)
class CourseHomepageRequiresPublicException(message: String) : FundedmindApiException(
    reason = ExtendedErrorReasonType.COURSE_HOMEPAGE_REQUIRES_PUBLIC,
    message = message,
)

@ResponseStatus(HttpStatus.UNPROCESSABLE_ENTITY)
class ActiveCoursesMismatchException(message: String) : FundedmindApiException(
    reason = ExtendedErrorReasonType.ACTIVE_COURSES_MISMATCH,
    message = message,
    shouldBeLoggedToSentry = false,
)

@ResponseStatus(HttpStatus.FORBIDDEN)
class CourseAttachmentsNotAccessibleToStudentException(message: String) : FundedmindApiException(
    reason = ExtendedErrorReasonType.COURSE_ATTACHMENTS_NOT_ACCESSIBLE_TO_STUDENT,
    message = message,
)

@ResponseStatus(HttpStatus.FORBIDDEN)
class CourseIsLockedForUserException(message: String) : FundedmindApiException(
    reason = ExtendedErrorReasonType.COURSE_IS_LOCKED_FOR_USER,
    message = message,
)
