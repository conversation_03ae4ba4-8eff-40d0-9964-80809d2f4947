package com.cleevio.fundedmind.domain.studentgamelevelhistory

import com.cleevio.fundedmind.domain.common.constant.GameLevel
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.time.Instant
import java.util.UUID

@Service
class CreateStudentGameLevelHistoryService(
    private val studentGameLevelHistoryRepository: StudentGameLevelHistoryRepository,
) {

    @Transactional
    fun create(
        studentId: UUID,
        gameLevel: GameLevel,
        achievedAt: Instant = Instant.now(),
    ): StudentGameLevelHistory = studentGameLevelHistoryRepository.save(
        StudentGameLevelHistory.newStudentGameLevelHistory(
            studentId = studentId,
            gameLevel = gameLevel,
            achievedAt = achievedAt,
        ),
    )
}
