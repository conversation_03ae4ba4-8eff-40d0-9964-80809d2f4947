package com.cleevio.fundedmind.domain.studentgamelevelhistory

import com.cleevio.fundedmind.application.common.util.UUIDv7
import com.cleevio.fundedmind.application.module.studentgamelevelhistory.exception.StudentGameLevelHistoryNotRelatedToStudentException
import com.cleevio.fundedmind.domain.DomainEntity
import com.cleevio.fundedmind.domain.common.constant.GameLevel
import jakarta.persistence.Entity
import jakarta.persistence.EnumType
import jakarta.persistence.Enumerated
import jakarta.persistence.Table
import org.hibernate.annotations.DynamicUpdate
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository
import java.time.Instant
import java.util.UUID

/**
 * New entry is created every time a student reaches a new level.
 * Used by FE to track if they should show a modal about a new level to the user.
 */
@Table(name = "student_game_level_history")
@Entity
@DynamicUpdate
class StudentGameLevelHistory private constructor(
    id: UUID,
    val studentId: UUID,
    @Enumerated(EnumType.STRING)
    val gameLevel: GameLevel,
    val achievedAt: Instant,
    shown: Boolean,
) : DomainEntity(id) {

    var shown: Boolean = shown
        private set

    companion object {
        fun newStudentGameLevelHistory(
            id: UUID = UUIDv7.randomUUID(),
            studentId: UUID,
            gameLevel: GameLevel,
            achievedAt: Instant,
        ) = StudentGameLevelHistory(
            id = id,
            studentId = studentId,
            gameLevel = gameLevel,
            achievedAt = achievedAt,
            shown = false,
        )
    }

    fun markAsShown() {
        this.shown = true
    }

    fun checkRelatedToStudent(studentId: UUID) {
        if (this.studentId != studentId) {
            throw StudentGameLevelHistoryNotRelatedToStudentException(
                "StudentGameLevelHistory: '$id' is not related to student: '$studentId'",
            )
        }
    }
}

@Repository
interface StudentGameLevelHistoryRepository : JpaRepository<StudentGameLevelHistory, UUID> {
    fun findByStudentId(studentId: UUID): List<StudentGameLevelHistory>
}
