package com.cleevio.fundedmind.domain.comment.exception

import com.cleevio.fundedmind.infrastructure.exception.ExtendedErrorReasonType
import com.cleevio.fundedmind.infrastructure.exception.FundedmindApiException
import org.springframework.http.HttpStatus
import org.springframework.web.bind.annotation.ResponseStatus

@ResponseStatus(HttpStatus.NOT_FOUND)
class ThreadCommentNotificationNotFoundException(message: String) : FundedmindApiException(
    reason = ExtendedErrorReasonType.THREAD_COMMENT_NOTIFICATION_NOT_FOUND,
    message = message,
)

@ResponseStatus(HttpStatus.NOT_FOUND)
class CommentNotFoundException(message: String) : FundedmindApiException(
    reason = ExtendedErrorReasonType.COMMENT_NOT_FOUND,
    message = message,
)

@ResponseStatus(HttpStatus.NOT_FOUND)
class CommentLikeNotFoundException(message: String) : FundedmindApiException(
    reason = ExtendedErrorReasonType.COMMENT_LIKE_NOT_FOUND,
    message = message,
)

@ResponseStatus(HttpStatus.FORBIDDEN)
class CommentOwnerIsWrongException(message: String) : FundedmindApiException(
    reason = ExtendedErrorReasonType.COMMENT_OWNER_IS_WRONG,
    message = message,
)
