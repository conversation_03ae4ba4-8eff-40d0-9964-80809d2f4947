package com.cleevio.fundedmind.domain.networking.exception

import com.cleevio.fundedmind.infrastructure.exception.ExtendedErrorReasonType
import com.cleevio.fundedmind.infrastructure.exception.FundedmindApiException
import org.springframework.http.HttpStatus
import org.springframework.web.bind.annotation.ResponseStatus

@ResponseStatus(HttpStatus.NOT_FOUND)
class NetworkingMessageNotFoundException(message: String) : FundedmindApiException(
    reason = ExtendedErrorReasonType.NETWORKING_MESSAGE_NOT_FOUND,
    message = message,
)

@ResponseStatus(HttpStatus.FORBIDDEN)
class NetworkingMessageNotAccessibleException(message: String) : FundedmindApiException(
    reason = ExtendedErrorReasonType.NETWORKING_MESSAGE_NOT_ACCESSIBLE,
    message = message,
)
