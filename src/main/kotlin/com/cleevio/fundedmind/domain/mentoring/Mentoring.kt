package com.cleevio.fundedmind.domain.mentoring

import com.cleevio.fundedmind.application.common.type.StripeSessionId
import com.cleevio.fundedmind.application.common.util.UUIDv7
import com.cleevio.fundedmind.domain.UpdatableEntity
import com.cleevio.fundedmind.domain.mentoring.exception.MentoringHasNoSessionsLeftException
import com.cleevio.fundedmind.domain.mentoring.exception.MentoringSessionUsageIsNegativeException
import jakarta.persistence.Entity
import jakarta.persistence.Table
import org.hibernate.annotations.DynamicUpdate
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.stereotype.Repository
import java.time.Instant
import java.util.UUID

/**
 * <PERSON><PERSON><PERSON><PERSON><PERSON> b<PERSON> / Mentoring
 */
@Table(name = "mentoring")
@Entity
@DynamicUpdate
class Mentoring private constructor(
    id: UUID,
    val sessionIdentifier: StripeSessionId,
    val studentId: UUID,
    val productId: UUID,
    val productName: String,
    val productAltDescription: String,
    val expiresAt: Instant?,
    val sessionCount: Int,
    usedSessions: Int,
) : UpdatableEntity(id) {
    var usedSessions: Int = usedSessions
        private set

    val sessionsLeft: Int
        get() = sessionCount - usedSessions

    val hasSessionsLeft: Boolean
        get() = sessionsLeft > 0

    val isExpired: Boolean
        get() = if (expiresAt == null) false else Instant.now() > expiresAt

    val boughtAt: Instant
        get() = createdAt

    companion object {
        fun newPurchasedMentoring(
            id: UUID = UUIDv7.randomUUID(),
            sessionIdentifier: StripeSessionId,
            studentId: UUID,
            productId: UUID,
            productName: String,
            productAltDescription: String,
            expiresAt: Instant?,
            sessionCount: Int,
        ) = Mentoring(
            id = id,
            sessionIdentifier = sessionIdentifier,
            studentId = studentId,
            productId = productId,
            productName = productName,
            productAltDescription = productAltDescription,
            expiresAt = expiresAt,
            sessionCount = sessionCount,
            usedSessions = 0,
        ).also {
            require(sessionCount > 0)
        }
    }

    fun useSession() {
        if (sessionsLeft == 0) {
            throw MentoringHasNoSessionsLeftException(
                "Mentoring: '$id' has no sessions left. Used sessions: $usedSessions/$sessionCount",
            )
        }

        this.usedSessions = this.usedSessions + 1
    }

    fun revertSessionUsage() {
        this.usedSessions = this.usedSessions - 1

        if (this.usedSessions < 0) {
            throw MentoringSessionUsageIsNegativeException(
                "Mentoring: '$id' has negative session usage: $usedSessions",
            )
        }
    }
}

@Repository
interface MentoringRepository : JpaRepository<Mentoring, UUID> {

    @Query(
        """
        SELECT m 
        FROM Mentoring m
        INNER JOIN Product p ON m.productId = p.id
        WHERE m.studentId = :studentId
        AND p.traderId = :traderId
        """,
    )
    fun findAllByStudentIdAndTraderId(
        studentId: UUID,
        traderId: UUID,
    ): List<Mentoring>

    fun existsByProductId(productId: UUID): Boolean

    @Query(
        """
        SELECT m 
        FROM Mentoring m
        INNER JOIN Product p ON m.productId = p.id
        AND p.traderId = :traderId
        """,
    )
    fun findAllByTraderId(traderId: UUID): List<Mentoring>
    fun findAllByProductId(productId: UUID): List<Mentoring>
}
