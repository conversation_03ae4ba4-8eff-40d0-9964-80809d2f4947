package com.cleevio.fundedmind.domain.referral.exception

import com.cleevio.fundedmind.infrastructure.exception.ExtendedErrorReasonType
import com.cleevio.fundedmind.infrastructure.exception.FundedmindApiException
import org.springframework.http.HttpStatus
import org.springframework.web.bind.annotation.ResponseStatus

@ResponseStatus(HttpStatus.NOT_FOUND)
class ReferralNotFoundException(message: String) : FundedmindApiException(
    reason = ExtendedErrorReasonType.REFERRAL_NOT_FOUND,
    message = message,
)

@ResponseStatus(HttpStatus.UNPROCESSABLE_ENTITY)
class ReferralMissingPictureException(message: String) : FundedmindApiException(
    reason = ExtendedErrorReasonType.REFERRAL_PICTURE_MISSING,
    message = message,
    shouldBeLoggedToSentry = false,
)

@ResponseStatus(HttpStatus.BAD_REQUEST)
class ReferralOrderCannotBeNegativeException(message: String) : FundedmindApiException(
    reason = ExtendedErrorReasonType.REFERRAL_ORDER_CANNOT_BE_NEGATIVE,
    message = message,
)

@ResponseStatus(HttpStatus.UNPROCESSABLE_ENTITY)
class ActiveReferralsMismatchException(message: String) : FundedmindApiException(
    reason = ExtendedErrorReasonType.ACTIVE_REFERRALS_MISMATCH,
    message = message,
    shouldBeLoggedToSentry = false,
)
