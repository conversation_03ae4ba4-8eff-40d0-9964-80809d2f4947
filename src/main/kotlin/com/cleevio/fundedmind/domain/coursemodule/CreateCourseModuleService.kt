package com.cleevio.fundedmind.domain.coursemodule

import com.cleevio.fundedmind.domain.common.AppButtonWithLink
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.util.UUID

@Service
class CreateCourseModuleService(
    private val courseModuleRepository: CourseModuleRepository,
) {

    @Transactional
    fun create(
        courseId: UUID,
        title: String,
        listingOrder: Int,
        description: String,
        shortDescription: String,
        rewardDescription: String?,
        rewardCouponCode: String?,
        rewardButton: AppButtonWithLink?,
        comingSoon: Boolean,
    ): CourseModule = courseModuleRepository.save(
        CourseModule.addNewModuleToCourse(
            courseId = courseId,
            title = title,
            listingOrder = listingOrder,
            description = description,
            shortDescription = shortDescription,
            rewardDescription = rewardDescription,
            rewardCouponCode = rewardCouponCode,
            rewardButton = rewardButton,
            comingSoon = comingSoon,
        ),
    )
}
