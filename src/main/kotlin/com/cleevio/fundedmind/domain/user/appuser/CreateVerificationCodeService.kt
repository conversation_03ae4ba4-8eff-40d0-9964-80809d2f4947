package com.cleevio.fundedmind.domain.user.appuser

import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.time.Instant
import java.util.UUID

@Service
class CreateVerificationCodeService(
    private val verificationCodeRepository: VerificationCodeRepository,
) {

    @Transactional
    fun createVerificationCode(
        appUserId: UUID,
        code: String,
        expiresAt: Instant,
    ): VerificationCode = verificationCodeRepository.save(
        VerificationCode.newVerificationCode(
            appUserId = appUserId,
            code = code,
            expiresAt = expiresAt,
        ),
    )
}
