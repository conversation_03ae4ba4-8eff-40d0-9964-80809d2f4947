package com.cleevio.fundedmind.domain.user.student

import com.cleevio.fundedmind.application.common.util.UUIDv7
import com.cleevio.fundedmind.domain.SoftDeletableEntity
import jakarta.persistence.Entity
import jakarta.persistence.Table
import org.hibernate.annotations.DynamicUpdate
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Modifying
import org.springframework.stereotype.Repository
import java.time.Instant
import java.util.UUID

/**
 * StudentDiscord represents a connection between a Student and their Discord account.
 */
@Table(name = "student_discord")
@Entity
@DynamicUpdate
class StudentDiscord private constructor(
    id: UUID,
    val studentId: UUID,
    val discordId: String,
    val userName: String?,
    val globalName: String?,
    val joinedAt: Instant,
) : SoftDeletableEntity(id) {

    companion object {
        fun newLinkedDiscordAccount(
            id: UUID = UUIDv7.randomUUID(),
            studentId: UUID,
            discordId: String,
            userName: String?,
            globalName: String?,
            joinedAt: Instant,
        ) = StudentDiscord(
            id = id,
            studentId = studentId,
            discordId = discordId,
            userName = userName,
            globalName = globalName,
            joinedAt = joinedAt,
        )
    }
}

@Repository
interface StudentDiscordRepository : JpaRepository<StudentDiscord, UUID> {
    fun findByStudentIdAndDeletedAtIsNull(studentId: UUID): StudentDiscord?
    fun existsByStudentIdAndDeletedAtIsNull(studentId: UUID): Boolean
    fun findAllByDeletedAtIsNull(): List<StudentDiscord>

    @Modifying
    fun deleteAllByStudentIdIn(studentIds: List<UUID>)
}
