package com.cleevio.fundedmind.domain.progress.service

import com.cleevio.fundedmind.domain.progress.CourseModuleProgressRepository
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.util.UUID

@Service
class DeleteCourseModuleProgressService(
    private val courseModuleProgressRepository: CourseModuleProgressRepository,
) {

    @Transactional
    fun deleteById(courseModuleProgressId: UUID) {
        courseModuleProgressRepository.deleteById(courseModuleProgressId)
    }
}
