package com.cleevio.fundedmind.domain.progress

import com.cleevio.fundedmind.application.common.util.UUIDv7
import com.cleevio.fundedmind.domain.DomainEntity
import jakarta.persistence.Entity
import jakarta.persistence.Table
import org.hibernate.annotations.DynamicUpdate
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository
import java.time.Instant
import java.util.UUID

/**
 * Záznam o dokončení modulu userom.
 *
 * Uniqueness: Only one row for combination of user and courseModule.
 */
@Table(name = "course_module_progress")
@Entity
@DynamicUpdate
class CourseModuleProgress private constructor(
    id: UUID,
    val userId: UUID,
    val courseModuleId: UUID,
    val finished: Boolean,
    val finishedAt: Instant,
) : DomainEntity(id) {

    companion object {
        fun newFinishedCourseModule(
            id: UUID = UUIDv7.randomUUID(),
            courseModuleId: UUID,
            userId: UUID,
            finishedAt: Instant,
        ) = CourseModuleProgress(
            id = id,
            courseModuleId = courseModuleId,
            userId = userId,
            finished = true,
            finishedAt = finishedAt,
        )
    }
}

@Repository
interface CourseModuleProgressRepository : JpaRepository<CourseModuleProgress, UUID> {
    fun findByCourseModuleIdAndUserId(
        courseModuleId: UUID,
        userId: UUID,
    ): CourseModuleProgress?

    fun findAllByUserIdAndCourseModuleIdIn(
        userId: UUID,
        courseModuleIds: List<UUID>,
    ): List<CourseModuleProgress>
}
