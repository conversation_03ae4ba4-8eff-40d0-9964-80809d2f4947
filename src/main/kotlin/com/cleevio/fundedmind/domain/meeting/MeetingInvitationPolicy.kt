package com.cleevio.fundedmind.domain.meeting

import com.cleevio.fundedmind.domain.common.constant.StudentTier
import com.cleevio.fundedmind.domain.common.constant.StudentTier.BASECAMP
import com.cleevio.fundedmind.domain.common.constant.StudentTier.EXCLUSIVE
import com.cleevio.fundedmind.domain.common.constant.StudentTier.MASTERCLASS
import com.cleevio.fundedmind.domain.common.exception.DiscordUsersMustBeInvitedException
import com.cleevio.fundedmind.domain.common.exception.StudentTierMustBeInvitedException

fun checkMeetingInvitationPolicy(
    invitedTiers: List<StudentTier>,
    invitedDiscordUsers: <PERSON><PERSON>an,
) {
    MeetingInvitationPolicy(invitedTiers, invitedDiscordUsers).check()
}

class MeetingInvitationPolicy(
    private val invitedTiers: List<StudentTier>,
    private val invitedDiscordUsers: <PERSON>olean,
) {
    fun check() {
        when {
            BASECAMP in invitedTiers -> {
                requires(MASTERCLASS, "$MASTERCLASS must be invited for $BASECAMP.")
                requires(EXCLUSIVE, "$EXCLUSIVE must be invited for $BASECAMP.")
                requiresDiscordUsers("Discord users must be invited for $BASECAMP.")
            }

            MASTERCLASS in invitedTiers -> {
                requires(EXCLUSIVE, "$EXCLUSIVE must be invited for $MASTERCLASS.")
            }

            EXCLUSIVE in invitedTiers -> {
                // No additional requirements for EXCLUSIVE
            }

            invitedDiscordUsers -> {
                // No additional requirements for only Discord users invitation
            }

            else -> {
                // No further requirements
            }
        }
    }

    private fun requires(
        tier: StudentTier,
        message: String,
    ) {
        if (tier !in invitedTiers) {
            throw StudentTierMustBeInvitedException(message)
        }
    }

    private fun requiresDiscordUsers(message: String) {
        if (!invitedDiscordUsers) {
            throw DiscordUsersMustBeInvitedException(message)
        }
    }
}
