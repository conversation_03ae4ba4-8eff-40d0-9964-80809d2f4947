package com.cleevio.fundedmind.adapter.out.jooq

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.application.common.query.AutocompleteFilter
import com.cleevio.fundedmind.jooq.tables.references.TRADER
import com.cleevio.fundedmind.parseIntegerList
import com.cleevio.fundedmind.toUUID
import io.kotest.matchers.shouldBe
import org.jooq.DSLContext
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource
import org.springframework.beans.factory.annotation.Autowired
import java.util.UUID

class AutocompleteFilterTest(
    @Autowired private val dslContext: DSLContext,
) : IntegrationTest() {

    @ParameterizedTest
    @CsvSource(
        delimiter = ';',
        nullValues = ["null"],
        value = [
            "null;  [1,2,3]",
            "xyz;  []",
            "adamovi<PERSON>;  [1,2]",
            "adamovic;  [1,2]",
            "adam; [1,2,3]",
            "ADAM; [1,2,3]",
            "aDAm; [1,2,3]",
            "fero;  [2]",
            "fé;  [2,3]",
            "%adam%;  []",
            "%;  []",
            "_;  []",
            "(;  []",
            ");  []",
        ],
    )
    fun `should create autocomplete condition using OR operator and apply it to query`(
        searchString: String?,
        expectedIdsRaw: String,
    ) {
        // given
        val expectedIds: List<UUID> = expectedIdsRaw.parseIntegerList().map { it.toUUID() }

        dataHelper.getTrader(id = 1.toUUID(), firstName = "Jožo", lastName = "Adamovič")
        dataHelper.getTrader(id = 2.toUUID(), firstName = "Fero", lastName = "Adamovic")
        dataHelper.getTrader(id = 3.toUUID(), firstName = "Adam", lastName = "Férenz")

        // when
        val underTest = object : AutocompleteFilter {
            override val searchString: String? = searchString
        }

        val conditions = listOfNotNull(
            underTest.autocompleteCondition(TRADER.FIRST_NAME, TRADER.LAST_NAME),
        )

        val result = dslContext
            .select(TRADER.ID)
            .from(TRADER)
            .where(conditions)
            .fetch()
            .map { it[TRADER.ID]!! }

        // then
        result shouldBe expectedIds
    }
}
