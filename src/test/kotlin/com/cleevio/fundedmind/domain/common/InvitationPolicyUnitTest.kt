package com.cleevio.fundedmind.domain.common

import com.cleevio.fundedmind.domain.common.constant.StudentTier
import com.cleevio.fundedmind.domain.common.exception.DiscordUsersMustBeInvitedException
import com.cleevio.fundedmind.domain.common.exception.StudentTierMustBeInvitedException
import com.cleevio.fundedmind.domain.meeting.MeetingInvitationPolicy
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test

class InvitationPolicyUnitTest {

    @Test
    fun `check - should pass when all Basecamp requirements are met`() {
        // given
        val invitedTiers = listOf(StudentTier.BASECAMP, StudentTier.MASTERCLASS, StudentTier.EXCLUSIVE)
        val invitedDiscordUsers = true
        val underTest = MeetingInvitationPolicy(invitedTiers, invitedDiscordUsers)

        // when
        val result = runCatching { underTest.check() }

        // then
        result.isSuccess shouldBe true
    }

    @Test
    fun `check - should throw when Basecamp is missing MASTERCLASS`() {
        // given
        val invitedTiers = listOf(StudentTier.BASECAMP, StudentTier.EXCLUSIVE)
        val invitedDiscordUsers = true
        val underTest = MeetingInvitationPolicy(invitedTiers, invitedDiscordUsers)

        // when
        val exception = shouldThrow<StudentTierMustBeInvitedException> {
            underTest.check()
        }

        // then
        exception.message shouldBe "MASTERCLASS must be invited for BASECAMP."
    }

    @Test
    fun `check - should throw when Basecamp is missing EXCLUSIVE`() {
        // given
        val invitedTiers = listOf(StudentTier.BASECAMP, StudentTier.MASTERCLASS)
        val invitedDiscordUsers = true
        val underTest = MeetingInvitationPolicy(invitedTiers, invitedDiscordUsers)

        // when
        val exception = shouldThrow<StudentTierMustBeInvitedException> {
            underTest.check()
        }

        // then
        exception.message shouldBe "EXCLUSIVE must be invited for BASECAMP."
    }

    @Test
    fun `check - should throw when Basecamp is missing Discord access`() {
        // given
        val invitedTiers = listOf(StudentTier.BASECAMP, StudentTier.MASTERCLASS, StudentTier.EXCLUSIVE)
        val invitedDiscordUsers = false
        val underTest = MeetingInvitationPolicy(invitedTiers, invitedDiscordUsers)

        // when
        val exception = shouldThrow<DiscordUsersMustBeInvitedException> {
            underTest.check()
        }

        // then
        exception.message shouldBe "Discord users must be invited for BASECAMP."
    }

    @Test
    fun `check - should pass when Masterclass requirements are met`() {
        // given
        val invitedTiers = listOf(StudentTier.MASTERCLASS, StudentTier.EXCLUSIVE)
        val invitedDiscordUsers = true
        val underTest = MeetingInvitationPolicy(invitedTiers, invitedDiscordUsers)

        // when
        val result = runCatching { underTest.check() }

        // then
        result.isSuccess shouldBe true
    }

    @Test
    fun `check - should throw when Masterclass is missing EXCLUSIVE`() {
        // given
        val invitedTiers = listOf(StudentTier.MASTERCLASS)
        val invitedDiscordUsers = true
        val underTest = MeetingInvitationPolicy(invitedTiers, invitedDiscordUsers)

        // when
        val exception = shouldThrow<StudentTierMustBeInvitedException> {
            underTest.check()
        }

        // then
        exception.message shouldBe "EXCLUSIVE must be invited for MASTERCLASS."
    }

    @Test
    fun `check - should pass for EXCLUSIVE only`() {
        // given
        val invitedTiers = listOf(StudentTier.EXCLUSIVE)
        val invitedDiscordUsers = false
        val underTest = MeetingInvitationPolicy(invitedTiers, invitedDiscordUsers)

        // when
        val result = runCatching { underTest.check() }

        // then
        result.isSuccess shouldBe true
    }

    @Test
    fun `check - should pass for only Discord users invited`() {
        // given
        val invitedTiers = emptyList<StudentTier>()
        val invitedDiscordUsers = true
        val underTest = MeetingInvitationPolicy(invitedTiers, invitedDiscordUsers)

        // when
        val result = runCatching { underTest.check() }

        // then
        result.isSuccess shouldBe true
    }

    @Test
    fun `check - should pass for no tiers and no Discord invitations`() {
        // given
        val invitedTiers = emptyList<StudentTier>()
        val invitedDiscordUsers = false
        val underTest = MeetingInvitationPolicy(invitedTiers, invitedDiscordUsers)

        // when
        val result = runCatching { underTest.check() }

        // then
        result.isSuccess shouldBe true
    }
}
