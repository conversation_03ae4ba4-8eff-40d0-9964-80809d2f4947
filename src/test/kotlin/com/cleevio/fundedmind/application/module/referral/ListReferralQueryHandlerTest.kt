package com.cleevio.fundedmind.application.module.referral

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.application.module.referral.query.ListReferralsQuery
import com.cleevio.fundedmind.domain.common.constant.StudentTier
import com.cleevio.fundedmind.domain.file.constant.FileType
import com.cleevio.fundedmind.domain.user.appuser.constant.UserRole
import com.cleevio.fundedmind.parseStudentTierList
import com.cleevio.fundedmind.toUUID
import io.kotest.matchers.collections.shouldContainExactlyInAnyOrder
import io.kotest.matchers.collections.shouldHaveSize
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource
import org.springframework.beans.factory.annotation.Autowired
import java.time.Instant
import java.time.temporal.ChronoUnit

class ListReferralQueryHandlerTest(
    @Autowired private val underTest: ListReferralQueryHandler,
) : IntegrationTest() {

    @Test
    fun `should list referrals - verify mappings`() {
        // given
        dataHelper.getAppUser(id = 1.toUUID(), userRole = UserRole.ADMIN)

        dataHelper.getReferral(
            id = 1.toUUID(),
            listingOrder = 1,
            title = "Referral 1",
            description = "Description 1",
            visibleToTiers = listOf(StudentTier.BASECAMP, StudentTier.MASTERCLASS, StudentTier.EXCLUSIVE),
            visibleToDiscordUsers = true,
            linkUrl = "referral-url",
            rewardCouponCode = "REWARD123",
            entityModifier = {
                it.changeImageDesktop(
                    fileId = dataHelper.getImage(
                        type = FileType.REFERRAL_DESKTOP_PHOTO,
                        originalFileUrl = "desktop-url",
                        compressedFileUrl = "desktop-url-comp",
                        blurHash = "123",
                    ).id,
                )
                it.changeImageMobile(
                    fileId = dataHelper.getImage(
                        type = FileType.REFERRAL_MOBILE_PHOTO,
                        originalFileUrl = "mobile-url",
                        compressedFileUrl = "mobile-url-comp",
                        blurHash = "456",
                    ).id,
                )
            },
        )

        // when
        val result = underTest.handle(
            ListReferralsQuery(userId = 1.toUUID()),
        )

        // then
        result.data shouldHaveSize 1
        result.data.single().run {
            referralId shouldBe 1.toUUID()
            listingOrder shouldBe 1
            published shouldBe false
            blurred shouldBe false
            imageDesktop shouldNotBe null
            imageDesktop!!.run {
                imageOriginalUrl shouldBe "desktop-url"
                imageCompressedUrl shouldBe "desktop-url-comp"
                imageBlurHash shouldBe "123"
            }
            imageMobile shouldNotBe null
            imageMobile!!.run {
                imageOriginalUrl shouldBe "mobile-url"
                imageCompressedUrl shouldBe "mobile-url-comp"
                imageBlurHash shouldBe "456"
            }
            title shouldBe "Referral 1"
            description shouldBe "Description 1"
            linkUrl shouldBe "referral-url"
            rewardCouponCode shouldBe "REWARD123"
            visibleToTiers shouldBe listOf(StudentTier.BASECAMP, StudentTier.MASTERCLASS, StudentTier.EXCLUSIVE)
            visibleToDiscordUsers shouldBe true
        }
    }

    @Test
    fun `admin should see all referrals`() {
        // given
        val admin = dataHelper.getAppUser(id = 1.toUUID(), userRole = UserRole.ADMIN)

        dataHelper.getReferral(id = 1.toUUID(), visibleToTiers = listOf(StudentTier.BASECAMP))
        dataHelper.getReferral(id = 2.toUUID(), visibleToTiers = listOf(StudentTier.MASTERCLASS))
        dataHelper.getReferral(id = 3.toUUID(), visibleToTiers = listOf(StudentTier.EXCLUSIVE))
        dataHelper.getReferral(id = 4.toUUID(), visibleToTiers = listOf(), visibleToDiscordUsers = true)
        dataHelper.getReferral(id = 5.toUUID(), visibleToTiers = listOf(), visibleToDiscordUsers = false)
        dataHelper.getReferral(
            id = 6.toUUID(),
            visibleToTiers = listOf(StudentTier.BASECAMP, StudentTier.MASTERCLASS, StudentTier.EXCLUSIVE),
            visibleToDiscordUsers = true,
            entityModifier = {
                it.changeImageDesktop(fileId = dataHelper.getImage(type = FileType.REFERRAL_DESKTOP_PHOTO).id)
                it.changeImageMobile(fileId = dataHelper.getImage(type = FileType.REFERRAL_MOBILE_PHOTO).id)
                it.publish()
            },
        )

        // when
        val result = underTest.handle(
            ListReferralsQuery(userId = admin.id),
        )

        // then
        result.data.map { it.referralId } shouldContainExactlyInAnyOrder setOf(
            1.toUUID(),
            2.toUUID(),
            3.toUUID(),
            4.toUUID(),
            5.toUUID(),
            6.toUUID(),
        )
    }

    @Test
    fun `trader should see only published referrals`() {
        // given
        val trader = dataHelper.getAppUser(id = 1.toUUID(), userRole = UserRole.TRADER)

        dataHelper.getReferral(id = 1.toUUID(), visibleToTiers = listOf(StudentTier.BASECAMP))
        dataHelper.getReferral(id = 2.toUUID(), visibleToTiers = listOf(StudentTier.MASTERCLASS))
        dataHelper.getReferral(id = 3.toUUID(), visibleToTiers = listOf(StudentTier.EXCLUSIVE))
        dataHelper.getReferral(id = 4.toUUID(), visibleToTiers = listOf(), visibleToDiscordUsers = true)
        dataHelper.getReferral(id = 5.toUUID(), visibleToTiers = listOf(), visibleToDiscordUsers = false)
        dataHelper.getReferral(
            id = 6.toUUID(),
            visibleToTiers = listOf(StudentTier.BASECAMP, StudentTier.MASTERCLASS, StudentTier.EXCLUSIVE),
            visibleToDiscordUsers = true,
            entityModifier = {
                it.changeImageDesktop(fileId = dataHelper.getImage(type = FileType.REFERRAL_DESKTOP_PHOTO).id)
                it.changeImageMobile(fileId = dataHelper.getImage(type = FileType.REFERRAL_MOBILE_PHOTO).id)
                it.publish()
            },
        )

        // when
        val result = underTest.handle(
            ListReferralsQuery(userId = trader.id),
        )

        // then
        result.data.map { it.referralId } shouldContainExactlyInAnyOrder setOf(6.toUUID())
    }

    @ParameterizedTest
    @CsvSource(
        value = [
            "[BASECAMP], false",
            "[MASTERCLASS], true",
            "[EXCLUSIVE], true",
            "[ ], true",
        ],
    )
    fun `should return empty referrals if PUBLISHED referral is not visible to student based on tier`(
        visibleTiers: String,
        shouldBeEmpty: Boolean?,
    ) {
        // given
        dataHelper.getAppUser(id = 1.toUUID(), userRole = UserRole.STUDENT)
        dataHelper.getStudent(id = 1.toUUID(), studentTier = StudentTier.BASECAMP)

        dataHelper.getReferral(
            id = 1.toUUID(),
            visibleToTiers = visibleTiers.parseStudentTierList(),
            entityModifier = {
                it.changeImageDesktop(dataHelper.getImage(type = FileType.REFERRAL_DESKTOP_PHOTO).id)
                it.changeImageMobile(dataHelper.getImage(type = FileType.REFERRAL_MOBILE_PHOTO).id)
                it.publish()
            },
        )

        // when
        val result = underTest.handle(
            ListReferralsQuery(userId = 1.toUUID()),
        )

        // then
        result.data.isEmpty() shouldBe shouldBeEmpty
    }

    @ParameterizedTest
    @CsvSource(
        value = [
            "[BASECAMP], true",
            "[MASTERCLASS], true",
            "[EXCLUSIVE], true",
            "[ ], true",
        ],
    )
    fun `should return empty referrals to student if referral is not PUBLISHED`(
        visibleTiers: String,
        shouldBeEmpty: Boolean?,
    ) {
        // given
        dataHelper.getAppUser(id = 1.toUUID(), userRole = UserRole.STUDENT)
        dataHelper.getStudent(id = 1.toUUID(), studentTier = StudentTier.BASECAMP)

        dataHelper.getReferral(
            id = 1.toUUID(),
            visibleToTiers = visibleTiers.parseStudentTierList(),
            entityModifier = { it.hide() },
        )

        // when
        val result = underTest.handle(
            ListReferralsQuery(userId = 1.toUUID()),
        )

        // then
        result.data.isEmpty() shouldBe shouldBeEmpty
    }

    @Test
    fun `should list referrals - should sort by listingOrder ASC`() {
        // given
        dataHelper.getAppUser(id = 1.toUUID(), userRole = UserRole.ADMIN)

        dataHelper.getReferral(id = 1.toUUID(), listingOrder = 2)
        dataHelper.getReferral(id = 2.toUUID(), listingOrder = 3)
        dataHelper.getReferral(id = 3.toUUID(), listingOrder = 1)

        // when
        val result = underTest.handle(
            ListReferralsQuery(userId = 1.toUUID()),
        )

        // then
        result.data shouldHaveSize 3
        result.data.map { it.referralId } shouldBe listOf(3.toUUID(), 1.toUUID(), 2.toUUID())
    }

    @Test
    fun `should list referrals - should filter out deleted referrals`() {
        // given
        dataHelper.getAppUser(id = 1.toUUID(), userRole = UserRole.ADMIN)

        dataHelper.getReferral(id = 1.toUUID())
        dataHelper.getReferral(id = 2.toUUID(), entityModifier = { it.softDelete() })

        // when
        val result = underTest.handle(
            ListReferralsQuery(userId = 1.toUUID()),
        )

        // then
        result.data shouldHaveSize 1
        result.data.map { it.referralId } shouldBe listOf(1.toUUID())
    }

    @Test
    fun `should blur referrals for BASECAMP students but not for other roles and tiers`() {
        // given
        // Create a referral
        dataHelper.getReferral(
            id = 1.toUUID(),
            linkUrl = "referral-url",
            rewardCouponCode = "REWARD123",
            visibleToTiers = listOf(StudentTier.BASECAMP, StudentTier.MASTERCLASS, StudentTier.EXCLUSIVE),
            entityModifier = {
                it.changeImageDesktop(
                    fileId = dataHelper.getImage(
                        type = FileType.REFERRAL_DESKTOP_PHOTO,
                        originalFileUrl = "desktop-url",
                        compressedFileUrl = "desktop-url-comp",
                        blurHash = "123",
                    ).id,
                )
                it.changeImageMobile(
                    fileId = dataHelper.getImage(
                        type = FileType.REFERRAL_MOBILE_PHOTO,
                        originalFileUrl = "mobile-url",
                        compressedFileUrl = "mobile-url-comp",
                        blurHash = "456",
                    ).id,
                )
                it.publish()
            },
        )

        // Test ADMIN role
        dataHelper.getAppUser(id = 2.toUUID(), userRole = UserRole.ADMIN)
        val adminResult = underTest.handle(ListReferralsQuery(userId = 2.toUUID()))
        adminResult.data.single().run {
            blurred shouldBe false
            linkUrl shouldBe linkUrl
            rewardCouponCode shouldBe rewardCouponCode
        }

        // Test TRADER role
        dataHelper.getAppUser(id = 3.toUUID(), userRole = UserRole.TRADER)
        val traderResult = underTest.handle(ListReferralsQuery(userId = 3.toUUID()))
        traderResult.data.single().run {
            blurred shouldBe false
            linkUrl shouldBe linkUrl
            rewardCouponCode shouldBe rewardCouponCode
        }

        // Test STUDENT with BASECAMP tier
        dataHelper.getAppUser(id = 4.toUUID(), userRole = UserRole.STUDENT)
        dataHelper.getStudent(id = 4.toUUID(), studentTier = StudentTier.BASECAMP)
        val basecampStudentResult = underTest.handle(ListReferralsQuery(userId = 4.toUUID()))
        basecampStudentResult.data.single().run {
            blurred shouldBe true
            linkUrl shouldBe null
            rewardCouponCode shouldBe null
        }

        // Test STUDENT with MASTERCLASS tier
        dataHelper.getAppUser(id = 5.toUUID(), userRole = UserRole.STUDENT)
        dataHelper.getStudent(id = 5.toUUID(), studentTier = StudentTier.MASTERCLASS)
        val masterclassStudentResult = underTest.handle(ListReferralsQuery(userId = 5.toUUID()))
        masterclassStudentResult.data.single().run {
            blurred shouldBe false
            linkUrl shouldBe linkUrl
            rewardCouponCode shouldBe rewardCouponCode
        }

        // Test STUDENT with EXCLUSIVE tier
        dataHelper.getAppUser(id = 6.toUUID(), userRole = UserRole.STUDENT)
        dataHelper.getStudent(id = 6.toUUID(), studentTier = StudentTier.EXCLUSIVE)
        val exclusiveStudentResult = underTest.handle(ListReferralsQuery(userId = 6.toUUID()))
        exclusiveStudentResult.data.single().run {
            blurred shouldBe false
            linkUrl shouldBe linkUrl
            rewardCouponCode shouldBe rewardCouponCode
        }
    }

    @Test
    fun `should show referrals to student with discord access when visibleToDiscordUsers is true`() {
        // given
        dataHelper.getAppUser(id = 1.toUUID(), userRole = UserRole.STUDENT)
        dataHelper.getStudent(
            id = 1.toUUID(),
            studentTier = StudentTier.BASECAMP,
            entityModifier = { it.activateDiscordSubscription(Instant.now().plus(1, ChronoUnit.DAYS)) },
        )

        dataHelper.getReferral(
            id = 1.toUUID(),
            visibleToTiers = listOf(), // Empty list means no tiers are allowed
            visibleToDiscordUsers = true, // But Discord users are allowed
            entityModifier = {
                it.changeImageDesktop(dataHelper.getImage(type = FileType.REFERRAL_DESKTOP_PHOTO).id)
                it.changeImageMobile(dataHelper.getImage(type = FileType.REFERRAL_MOBILE_PHOTO).id)
                it.publish()
            },
        )

        // when
        val result = underTest.handle(
            ListReferralsQuery(userId = 1.toUUID()),
        )

        // then
        result.data shouldHaveSize 1
        result.data.single().referralId shouldBe 1.toUUID()
    }

    @Test
    fun `should not show referrals to student without discord access when only visibleToDiscordUsers is true`() {
        // given
        dataHelper.getAppUser(id = 1.toUUID(), userRole = UserRole.STUDENT)
        dataHelper.getStudent(
            id = 1.toUUID(),
            studentTier = StudentTier.BASECAMP,
            // No Discord subscription activated
            entityModifier = { it.deactivateDiscordSubscription() },
        )

        dataHelper.getReferral(
            id = 1.toUUID(),
            visibleToTiers = listOf(), // Empty list means no tiers are allowed
            visibleToDiscordUsers = true, // But Discord users are allowed
            entityModifier = {
                it.changeImageDesktop(dataHelper.getImage(type = FileType.REFERRAL_DESKTOP_PHOTO).id)
                it.changeImageMobile(dataHelper.getImage(type = FileType.REFERRAL_MOBILE_PHOTO).id)
                it.publish()
            },
        )

        // when
        val result = underTest.handle(
            ListReferralsQuery(userId = 1.toUUID()),
        )

        // then
        result.data shouldHaveSize 0
    }
}
