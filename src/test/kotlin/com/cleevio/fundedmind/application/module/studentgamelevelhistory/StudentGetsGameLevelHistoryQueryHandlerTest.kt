package com.cleevio.fundedmind.application.module.studentgamelevelhistory

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.application.module.studentgamelevelhistory.query.StudentGetsGameLevelHistoryQuery
import com.cleevio.fundedmind.domain.common.constant.GameLevel
import com.cleevio.fundedmind.shouldBeAbout
import com.cleevio.fundedmind.toInstant
import com.cleevio.fundedmind.toUUID
import io.kotest.matchers.collections.shouldHaveSize
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired

class StudentGetsGameLevelHistoryQueryHandlerTest(
    @Autowired private val underTest: StudentGetsGameLevelHistoryQueryHandler,
) : IntegrationTest() {

    @Test
    fun `should return student game level history list ordered by achieved at desc`() {
        // Given
        val student = dataHelper.getStudent(id = 1.toUUID())
        val history1 = dataHelper.getStudentGameLevelHistory(
            id = 1.toUUID(),
            studentId = student.id,
            gameLevel = GameLevel.FIVE,
            achievedAt = "2024-01-01T10:00:00Z".toInstant(),
            entityModifier = { it.markAsShown() },
        )
        val history2 = dataHelper.getStudentGameLevelHistory(
            id = 2.toUUID(),
            studentId = student.id,
            gameLevel = GameLevel.SIX,
            achievedAt = "2024-01-02T10:00:00Z".toInstant(),
            entityModifier = { it.markAsShown() },
        )
        val history3 = dataHelper.getStudentGameLevelHistory(
            id = 3.toUUID(),
            studentId = student.id,
            gameLevel = GameLevel.SEVEN,
            achievedAt = "2024-01-03T10:00:00Z".toInstant(),
        )

        // When
        val result = underTest.handle(
            StudentGetsGameLevelHistoryQuery(studentId = student.id),
        )

        // Then
        result.data shouldHaveSize 3
        result.data[0].run {
            id shouldBe history3.id
            level shouldBe GameLevel.SEVEN
            achievedAt shouldBe "2024-01-03T10:00:00Z".toInstant()
            shown shouldBe false
        }
        result.data[1].run {
            id shouldBe history2.id
            level shouldBe GameLevel.SIX
            achievedAt shouldBeAbout "2024-01-02T10:00:00Z".toInstant()
            shown shouldBe true
        }
        result.data[2].run {
            id shouldBe history1.id
            level shouldBe GameLevel.FIVE
            achievedAt shouldBe "2024-01-01T10:00:00Z".toInstant()
            shown shouldBe true
        }
    }

    @Test
    fun `should return empty list when student has no game level history`() {
        // Given
        val student = dataHelper.getStudent(id = 1.toUUID())

        // When
        val result = underTest.handle(
            StudentGetsGameLevelHistoryQuery(studentId = student.id),
        )

        // Then
        result.data shouldHaveSize 0
    }

    @Test
    fun `should return only history for specified student`() {
        // Given
        val student1 = dataHelper.getStudent(id = 1.toUUID())
        val student2 = dataHelper.getStudent(id = 2.toUUID())

        dataHelper.getStudentGameLevelHistory(
            id = 1.toUUID(),
            studentId = student1.id,
            gameLevel = GameLevel.FIVE,
        )

        dataHelper.getStudentGameLevelHistory(
            id = 2.toUUID(),
            studentId = student2.id, // different student
            gameLevel = GameLevel.SIX,
        )

        // When
        val result = underTest.handle(
            StudentGetsGameLevelHistoryQuery(studentId = student1.id),
        )

        // Then
        result.data shouldHaveSize 1
        result.data.single().id shouldBe 1.toUUID()
    }
}
