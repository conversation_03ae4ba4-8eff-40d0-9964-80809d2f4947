package com.cleevio.fundedmind.application.module.mentoring

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.application.module.mentoring.query.StudentGetsTheirMentoringsQuery
import com.cleevio.fundedmind.domain.common.constant.BadgeColor
import com.cleevio.fundedmind.domain.common.constant.TraderMentoring
import com.cleevio.fundedmind.domain.file.constant.FileType
import com.cleevio.fundedmind.toInstant
import com.cleevio.fundedmind.toUUID
import io.kotest.matchers.collections.shouldHaveSize
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired

class StudentGetsTheirMentoringsQueryHandlerTest(
    @Autowired private val underTest: StudentGetsTheirMentoringsQueryHandler,
) : IntegrationTest() {

    @Test
    fun `student gets their mentorings - verify mappings`() {
        // given
        val student1 = dataHelper.getStudent(id = 1.toUUID())
        val student2 = dataHelper.getStudent(id = 2.toUUID())

        val trader1 = dataHelper.getTrader(
            id = 1.toUUID(),
            position = "Mentor",
            firstName = "John",
            lastName = "Doe",
            badgeColor = BadgeColor.GOLDEN_GRADIENT,
            calendlyUrl = "calendly-url",
            calendlyUserUri = "user-uri",
            entityModifier = {
                it.changeProfilePicture(
                    dataHelper.getImage(
                        type = FileType.TRADER_PROFILE_PICTURE,
                        originalFileUrl = "url",
                        compressedFileUrl = "url-comp",
                        blurHash = "123",
                    ).id,
                )
            },
        )
        val trader2 = dataHelper.getTrader(
            id = 2.toUUID(),
            position = "Mentor",
            firstName = "Jane",
            lastName = "Dede",
            badgeColor = BadgeColor.BLUE_GRADIENT,
            calendlyUrl = "calendly-url2",
            calendlyUserUri = "user-uri2",
            entityModifier = {
                it.changeProfilePicture(
                    dataHelper.getImage(
                        type = FileType.TRADER_PROFILE_PICTURE,
                        originalFileUrl = "url2",
                        compressedFileUrl = "url2-comp",
                        blurHash = "456",
                    ).id,
                )
            },
        )

        val product11 = dataHelper.getProduct(id = 1.toUUID(), traderId = trader1.id)
        val product12 = dataHelper.getProduct(id = 3.toUUID(), traderId = trader1.id)

        val product2 = dataHelper.getProduct(id = 2.toUUID(), traderId = trader2.id)

        // fully used mentoring for product 1
        dataHelper.getMentoring(
            id = 1.toUUID(),
            studentId = student1.id,
            productId = product11.id,
            productName = "1",
            productAltDescription = "alt11",
            sessionCount = 5,
            useSessions = 5,
            expiresAt = "2025-02-01T23:59:59.000Z".toInstant(), // 01.02. 23:59:59
            createdTimestamp = "2025-01-01T10:00:00.000Z".toInstant(), // 01.01. 10:00
        )

        // partially used mentoring for product 1
        dataHelper.getMentoring(
            id = 2.toUUID(),
            studentId = student1.id,
            productId = product11.id,
            productName = "1",
            productAltDescription = "alt11",
            sessionCount = 5,
            useSessions = 3,
            expiresAt = "2025-04-01T23:59:59.000Z".toInstant(), // 01.04. 23:59:59
            createdTimestamp = "2025-03-01T10:00:00.000Z".toInstant(), // 01.03. 10:00
        )

        dataHelper.getMentoring(
            id = 3.toUUID(),
            studentId = student1.id,
            productId = product2.id,
            productName = "2",
            productAltDescription = "alt2",
            sessionCount = 10,
            useSessions = 0,
            expiresAt = "2025-03-01T23:59:59.000Z".toInstant(), // 01.03. 23:59:59
            createdTimestamp = "2025-02-01T10:00:00.000Z".toInstant(), // 01.02. 10:00
        )

        dataHelper.getMentoring(
            id = 4.toUUID(),
            studentId = student2.id, // different student
            productId = product11.id,
        )

        dataHelper.getMentoring(
            id = 5.toUUID(),
            studentId = student1.id,
            productId = product2.id,
            productName = "2",
            productAltDescription = "alt2",
            sessionCount = 3,
            useSessions = 1,
            expiresAt = null,
            createdTimestamp = "2025-01-01T08:00:00.000Z".toInstant(), // 01.01. 08:00
        )

        // when
        val result = underTest.handle(
            StudentGetsTheirMentoringsQuery(studentId = student1.id),
        )

        // then
        result.data shouldHaveSize 4

        result.data.run {
            this[0].run {
                mentoringId shouldBe 2.toUUID()
                boughtAt shouldBe "2025-03-01T10:00:00.000Z".toInstant()
                productId shouldBe 1.toUUID()
                productName shouldBe "1"
                productAltDescription shouldBe "alt11"
                expiresAt shouldBe "2025-04-01T23:59:59.000Z".toInstant()
                sessionCount shouldBe 5
                usedSessions shouldBe 3
                trader.run {
                    traderId shouldBe 1.toUUID()
                    position shouldBe "Mentor"
                    firstName shouldBe "John"
                    lastName shouldBe "Doe"
                    profilePicture!!.run {
                        imageOriginalUrl shouldBe "url"
                        imageCompressedUrl shouldBe "url-comp"
                        imageBlurHash shouldBe "123"
                    }
                    badgeColor shouldBe BadgeColor.GOLDEN_GRADIENT
                    calendlyUrl shouldBe "calendly-url"
                    mentoring shouldBe TraderMentoring.YES
                }
            }
            this[1].run {
                mentoringId shouldBe 3.toUUID()
                boughtAt shouldBe "2025-02-01T10:00:00.000Z".toInstant()
                productId shouldBe 2.toUUID()
                productName shouldBe "2"
                productAltDescription shouldBe "alt2"
                expiresAt shouldBe "2025-03-01T23:59:59.000Z".toInstant()
                sessionCount shouldBe 10
                usedSessions shouldBe 0
                trader.run {
                    traderId shouldBe 2.toUUID()
                    position shouldBe "Mentor"
                    firstName shouldBe "Jane"
                    lastName shouldBe "Dede"
                    profilePicture!!.run {
                        imageOriginalUrl shouldBe "url2"
                        imageCompressedUrl shouldBe "url2-comp"
                        imageBlurHash shouldBe "456"
                    }
                    badgeColor shouldBe BadgeColor.BLUE_GRADIENT
                    calendlyUrl shouldBe "calendly-url2"
                    mentoring shouldBe TraderMentoring.YES
                }
            }
            this[2].run {
                mentoringId shouldBe 1.toUUID()
                boughtAt shouldBe "2025-01-01T10:00:00.000Z".toInstant()
                productId shouldBe 1.toUUID()
                productName shouldBe "1"
                productAltDescription shouldBe "alt11"
                expiresAt shouldBe "2025-02-01T23:59:59.000Z".toInstant()
                sessionCount shouldBe 5
                usedSessions shouldBe 5
                trader.run {
                    traderId shouldBe 1.toUUID()
                    position shouldBe "Mentor"
                    firstName shouldBe "John"
                    lastName shouldBe "Doe"
                    profilePicture!!.run {
                        imageOriginalUrl shouldBe "url"
                        imageCompressedUrl shouldBe "url-comp"
                        imageBlurHash shouldBe "123"
                    }
                    badgeColor shouldBe BadgeColor.GOLDEN_GRADIENT
                    calendlyUrl shouldBe "calendly-url"
                    mentoring shouldBe TraderMentoring.YES
                }
            }
            this[3].run {
                mentoringId shouldBe 5.toUUID()
                boughtAt shouldBe "2025-01-01T08:00:00.000Z".toInstant()
                productId shouldBe 2.toUUID()
                productName shouldBe "2"
                productAltDescription shouldBe "alt2"
                expiresAt shouldBe null
                sessionCount shouldBe 3
                usedSessions shouldBe 1
                trader.run {
                    traderId shouldBe 2.toUUID()
                    position shouldBe "Mentor"
                    firstName shouldBe "Jane"
                    lastName shouldBe "Dede"
                    profilePicture!!.run {
                        imageOriginalUrl shouldBe "url2"
                        imageCompressedUrl shouldBe "url2-comp"
                        imageBlurHash shouldBe "456"
                    }
                    badgeColor shouldBe BadgeColor.BLUE_GRADIENT
                    calendlyUrl shouldBe "calendly-url2"
                    mentoring shouldBe TraderMentoring.YES
                }
            }
        }
    }

    @Test
    fun `student gets their mentorings but has no mentorings should return empty list`() {
        // given
        val student1 = dataHelper.getStudent(id = 1.toUUID())
        val student2 = dataHelper.getStudent(id = 2.toUUID())

        dataHelper.getMentoring(
            studentId = student2.id, // different student
            productId = dataHelper.getProduct(traderId = dataHelper.getTrader().id).id,
        )

        // when
        val result = underTest.handle(
            StudentGetsTheirMentoringsQuery(studentId = student1.id),
        )

        // then
        result.data.size shouldBe 0
    }
}
