package com.cleevio.fundedmind.application.module.studentgamelevelhistory

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.application.module.studentgamelevelhistory.command.StudentMarksGameLevelHistoryShownCommand
import com.cleevio.fundedmind.application.module.studentgamelevelhistory.exception.StudentGameLevelHistoryNotFoundException
import com.cleevio.fundedmind.application.module.studentgamelevelhistory.exception.StudentGameLevelHistoryNotRelatedToStudentException
import com.cleevio.fundedmind.domain.common.constant.GameLevel
import com.cleevio.fundedmind.domain.studentgamelevelhistory.StudentGameLevelHistoryRepository
import com.cleevio.fundedmind.toInstant
import com.cleevio.fundedmind.toUUID
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.repository.findByIdOrNull

class StudentMarksGameLevelHistoryShownHandlerTest(
    @Autowired private val underTest: StudentMarksGameLevelHistoryShownHandler,
    @Autowired private val studentGameLevelHistoryRepository: StudentGameLevelHistoryRepository,
) : IntegrationTest() {

    @Test
    fun `should mark student game level history as shown`() {
        // Given
        val student = dataHelper.getStudent(id = 1.toUUID())
        val history = dataHelper.getStudentGameLevelHistory(
            id = 1.toUUID(),
            studentId = student.id,
            gameLevel = GameLevel.FIVE,
            achievedAt = "2024-01-01T10:00:00Z".toInstant(),
        )

        // When
        underTest.handle(
            StudentMarksGameLevelHistoryShownCommand(
                studentGameLevelHistoryId = history.id,
                studentId = student.id,
            ),
        )

        // Then
        studentGameLevelHistoryRepository.findByIdOrNull(history.id)!!.run {
            shown shouldBe true
        }
    }

    @Test
    fun `should pass if marking already shown student game level history`() {
        // Given
        val student = dataHelper.getStudent(id = 1.toUUID())
        val history = dataHelper.getStudentGameLevelHistory(
            id = 1.toUUID(),
            studentId = student.id,
            gameLevel = GameLevel.FIVE,
            achievedAt = "2024-01-01T10:00:00Z".toInstant(),
            entityModifier = { it.markAsShown() },
        )

        // When
        underTest.handle(
            StudentMarksGameLevelHistoryShownCommand(
                studentGameLevelHistoryId = history.id,
                studentId = student.id,
            ),
        )

        // Then
        studentGameLevelHistoryRepository.findByIdOrNull(history.id)!!.run {
            shown shouldBe true // no change
        }
    }

    @Test
    fun `should throw exception when student game level history not found`() {
        shouldThrow<StudentGameLevelHistoryNotFoundException> {
            underTest.handle(
                StudentMarksGameLevelHistoryShownCommand(
                    studentGameLevelHistoryId = 999.toUUID(),
                    studentId = 1.toUUID(),
                ),
            )
        }
    }

    @Test
    fun `should throw if history is not related to student`() {
        // Given
        val history = dataHelper.getStudentGameLevelHistory(
            id = 1.toUUID(),
            studentId = dataHelper.getStudent(id = 1.toUUID()).id,
        )

        // When
        shouldThrow<StudentGameLevelHistoryNotRelatedToStudentException> {
            underTest.handle(
                StudentMarksGameLevelHistoryShownCommand(
                    studentGameLevelHistoryId = history.id,
                    studentId = 999.toUUID(),
                ),
            )
        }
    }
}
