package com.cleevio.fundedmind.application.module.mentoringmeeting

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.application.common.port.out.CancelCalendlyEventPort.CancelReason
import com.cleevio.fundedmind.application.module.mentoringmeeting.command.ProcessEventBookingCommand
import com.cleevio.fundedmind.domain.common.constant.Color
import com.cleevio.fundedmind.domain.common.constant.StudentTier
import com.cleevio.fundedmind.domain.mentoring.MentoringRepository
import com.cleevio.fundedmind.domain.mentoringmeeting.MentoringMeetingRepository
import com.cleevio.fundedmind.domain.user.appuser.constant.UserRole
import com.cleevio.fundedmind.toInstant
import com.cleevio.fundedmind.toUUID
import io.kotest.matchers.shouldBe
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.verify
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.repository.findByIdOrNull

class ProcessEventBookingCommandHandlerTest(
    @Autowired private val underTest: ProcessEventBookingCommandHandler,
    @Autowired private val mentoringRepository: MentoringRepository,
    @Autowired private val mentoringMeetingRepository: MentoringMeetingRepository,
) : IntegrationTest() {

    @Test
    fun `should create mentoring meeting for student with available mentoring`() {
        // given
        val student = dataHelper.getAppUser(
            id = 1.toUUID(),
            userRole = UserRole.STUDENT,
            email = "<EMAIL>",
        ).also {
            dataHelper.getStudent(id = it.id, studentTier = StudentTier.MASTERCLASS)
        }

        val product = dataHelper.getProduct(
            id = 3.toUUID(),
            sessionsCount = 5,
            traderId = dataHelper.getTrader(
                id = 2.toUUID(),
                calendlyUrl = "calendly-url",
                calendlyUserUri = "calendly-user-uri",
            ).id,
        )

        val mentoring = dataHelper.getMentoring(
            id = 0.toUUID(),
            studentId = student.id,
            productId = product.id,
            sessionCount = 5,
            useSessions = 0,
            createdTimestamp = "2025-01-01T00:00:00Z".toInstant(), // 01.01. 10:00
        )

        // when
        underTest.handle(
            ProcessEventBookingCommand(
                traderUri = "calendly-user-uri",
                studentEmail = "<EMAIL>",
                startAt = "2025-08-15T14:00:00Z".toInstant(),
                finishAt = "2025-08-15T15:00:00Z".toInstant(),
                scheduledEventUri = "event-uri",
                meetingUrl = "meeting-url",
            ),
        )

        // then
        mentoringRepository.findByIdOrNull(mentoring.id)!!.usedSessions shouldBe 1
        mentoringMeetingRepository.findAll().run {
            size shouldBe 1
            single().run {
                mentoringId shouldBe mentoring.id
                calendlyEventUri shouldBe "event-uri"
                color shouldBe Color.WHITE
                startAt shouldBe "2025-08-15T14:00:00Z".toInstant()
                finishAt shouldBe "2025-08-15T15:00:00Z".toInstant()
                meetingUrl shouldBe "meeting-url"
                recordingUrl shouldBe null
                modification shouldBe null
            }
        }
    }

    @Test
    fun `should use first available mentoring when multiple exist`() {
        // given
        val student = dataHelper.getAppUser(
            id = 1.toUUID(),
            userRole = UserRole.STUDENT,
            email = "<EMAIL>",
        ).also {
            dataHelper.getStudent(id = it.id, studentTier = StudentTier.MASTERCLASS)
        }

        val trader = dataHelper.getTrader(
            id = 2.toUUID(),
            calendlyUrl = "calendly-url",
            calendlyUserUri = "calendly-user-uri",
        )

        val product1 = dataHelper.getProduct(
            id = 31.toUUID(),
            sessionsCount = 5,
            traderId = trader.id,
        )
        val product2 = dataHelper.getProduct(
            id = 32.toUUID(),
            sessionsCount = 10,
            traderId = trader.id,
        )

        // Create two mentorings - one older, one newer
        val olderMentoring = dataHelper.getMentoring(
            studentId = student.id,
            productId = product1.id,
            sessionCount = 5,
            useSessions = 0,
            createdTimestamp = "2025-01-01T00:00:00Z".toInstant(), // 01.01. 10:00
        )

        val newerMentoring = dataHelper.getMentoring(
            studentId = student.id,
            productId = product2.id,
            sessionCount = 10,
            useSessions = 0,
            createdTimestamp = "2025-06-01T00:00:00Z".toInstant(), // 01.06. 10:00
        )

        // when
        underTest.handle(
            ProcessEventBookingCommand(
                traderUri = "calendly-user-uri",
                studentEmail = "<EMAIL>",
                startAt = "2025-08-15T14:00:00Z".toInstant(), // 15.08. 14:00
                finishAt = "2025-08-15T15:00:00Z".toInstant(), // 15.08. 15:00
                scheduledEventUri = "event-uri",
                meetingUrl = "meeting-url",
            ),
        )

        // Verify older mentoring session was used
        mentoringRepository.findById(newerMentoring.id).get().usedSessions shouldBe 0
        mentoringRepository.findById(olderMentoring.id).get().usedSessions shouldBe 1
        mentoringMeetingRepository.findAll().run {
            size shouldBe 1
            single().run {
                mentoringId shouldBe olderMentoring.id
                calendlyEventUri shouldBe "event-uri"
                color shouldBe Color.WHITE
                startAt shouldBe "2025-08-15T14:00:00Z".toInstant()
                finishAt shouldBe "2025-08-15T15:00:00Z".toInstant()
                meetingUrl shouldBe "meeting-url"
                recordingUrl shouldBe null
                modification shouldBe null
            }
        }
    }

    @Test
    fun `should not throw exception when no mentoring is available`() {
        every {
            calendlyService.cancelCalendlyEvent(
                scheduledEventUri = "event-uri",
                reason = any(),
            )
        } just Runs

        // given
        val student = dataHelper.getAppUser(
            id = 1.toUUID(),
            userRole = UserRole.STUDENT,
            email = "<EMAIL>",
        ).also {
            dataHelper.getStudent(
                id = it.id,
                studentTier = StudentTier.MASTERCLASS,
                firstName = "John",
                lastName = "Doe",
            )
        }

        val product = dataHelper.getProduct(
            id = 3.toUUID(),
            sessionsCount = 5,
            traderId = dataHelper.getTrader(
                id = 2.toUUID(),
                firstName = "Trader",
                lastName = "Jorge",
                calendlyUrl = "calendly-url",
                calendlyUserUri = "calendly-user-uri",
            ).id,
        )

        // Create mentoring with no sessions left
        val mentoring = dataHelper.getMentoring(
            id = 0.toUUID(),
            studentId = student.id,
            productId = product.id,
            sessionCount = 5,
            useSessions = 5,
            createdTimestamp = "2025-01-01T00:00:00Z".toInstant(), // 01.01. 10:00
        )

        // when - should not throw
        underTest.handle(
            ProcessEventBookingCommand(
                traderUri = "calendly-user-uri",
                studentEmail = "<EMAIL>",
                startAt = "2025-08-15T14:00:00Z".toInstant(), // 15.08. 14:00
                finishAt = "2025-08-15T15:00:00Z".toInstant(), // 15.08. 15:00
                scheduledEventUri = "event-uri",
                meetingUrl = "meeting-url",
            ),
        )

        // then - no created mentoring meetings
        mentoringMeetingRepository.findAll().size shouldBe 0

        verify {
            calendlyService.cancelCalendlyEvent(
                scheduledEventUri = "event-uri",
                reason = CancelReason.noAvailableMentoring(
                    studentFirstName = "John",
                    studentLastName = "Doe",
                    traderFirstName = "Trader",
                    traderLastName = "Jorge",
                ),
            )
        }
    }

    @Test
    fun `should not cancel scheduled event when user is not found`() {
        // given
        val trader = dataHelper.getTrader(
            id = 1.toUUID(),
            calendlyUrl = "calendly-url",
            calendlyUserUri = "calendly-user-uri",
        )

        // when - should not throw with non-existent email
        underTest.handle(
            ProcessEventBookingCommand(
                traderUri = "calendly-user-uri",
                studentEmail = "<EMAIL>",
                startAt = "2025-08-15T14:00:00Z".toInstant(), // 15.08. 14:00
                finishAt = "2025-08-15T15:00:00Z".toInstant(), // 15.08. 15:00
                scheduledEventUri = "event-uri",
                meetingUrl = "meeting-url",
            ),
        )

        // then - no created mentoring meetings
        mentoringMeetingRepository.findAll().size shouldBe 0

        verify(exactly = 0) {
            calendlyService.cancelCalendlyEvent(
                scheduledEventUri = "event-uri",
                reason = CancelReason.emailIsUnknown("<EMAIL>"),
            )
        }
    }

    @Test
    fun `should cancel scheduled event when user has wrong role`() {
        // given
        val adminUser = dataHelper.getAppUser(
            id = 1.toUUID(),
            userRole = UserRole.ADMIN, // non-student user
            email = "<EMAIL>",
        )

        val trader = dataHelper.getTrader(
            id = 2.toUUID(),
            calendlyUrl = "calendly-url",
            calendlyUserUri = "calendly-user-uri",
        )

        every {
            calendlyService.cancelCalendlyEvent(
                scheduledEventUri = "event-uri",
                reason = any(),
            )
        } just Runs

        // when - should not throw
        underTest.handle(
            ProcessEventBookingCommand(
                traderUri = "calendly-user-uri",
                studentEmail = "<EMAIL>", // User with wrong role
                startAt = "2025-08-15T14:00:00Z".toInstant(), // 15.08. 14:00
                finishAt = "2025-08-15T15:00:00Z".toInstant(), // 15.08. 15:00
                scheduledEventUri = "event-uri",
                meetingUrl = "meeting-url",
            ),
        )

        // then - no created mentoring meetings
        mentoringMeetingRepository.findAll().size shouldBe 0

        verify {
            calendlyService.cancelCalendlyEvent(
                scheduledEventUri = "event-uri",
                reason = CancelReason.unknownError(),
            )
        }
    }

    @Test
    fun `should not cancel scheduled event when trader is not found`() {
        // given
        val student = dataHelper.getAppUser(
            id = 1.toUUID(),
            userRole = UserRole.STUDENT,
            email = "<EMAIL>",
        ).also {
            dataHelper.getStudent(id = it.id, studentTier = StudentTier.MASTERCLASS)
        }

        // No trader with this calendly URI
        val unknownTraderUri = "unknown-calendly-user-uri"

        // when - should not throw
        underTest.handle(
            ProcessEventBookingCommand(
                traderUri = unknownTraderUri,
                studentEmail = "<EMAIL>",
                startAt = "2025-08-15T14:00:00Z".toInstant(), // 15.08. 14:00
                finishAt = "2025-08-15T15:00:00Z".toInstant(), // 15.08. 15:00
                scheduledEventUri = "event-uri",
                meetingUrl = "meeting-url",
            ),
        )

        // then - no created mentoring meetings
        mentoringMeetingRepository.findAll().size shouldBe 0

        verify(exactly = 0) {
            calendlyService.cancelCalendlyEvent(scheduledEventUri = any(), reason = any())
        }
    }
}
