package com.cleevio.fundedmind.application.module.payment

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.application.common.port.out.GetSubscriptionDataPort
import com.cleevio.fundedmind.application.module.payment.command.ProcessSubscriptionCancelIntentCommand
import com.cleevio.fundedmind.domain.common.constant.StudentTier
import com.cleevio.fundedmind.domain.common.constant.SubscriptionStatus
import com.cleevio.fundedmind.domain.user.appuser.constant.UserRole
import com.cleevio.fundedmind.toInstant
import com.cleevio.fundedmind.toUUID
import io.kotest.assertions.throwables.shouldThrow
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.verify
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired

class ProcessSubscriptionCancelIntentCommandHandlerTest(
    @Autowired private val underTest: ProcessSubscriptionCancelIntentCommandHandler,
) : IntegrationTest() {

    @Test
    fun `process subscription cancel intent - should send cancellation email`() {
        // given
        dataHelper.getAppUser(
            id = 1.toUUID(),
            userRole = UserRole.STUDENT,
            stripeIdentifier = "cus_1",
            hubspotIdentifier = 1,
        ).also {
            dataHelper.getStudent(
                id = 1.toUUID(),
                studentTier = StudentTier.MASTERCLASS,
                entityModifier = {
                    it.activateDiscordSubscription("2025-02-01T23:59:00Z".toInstant())
                },
            )
        }

        every {
            stripeService.getSubscriptionBySubscriptionId("sub_1")
        } returns GetSubscriptionDataPort.SubscriptionData(
            subscriptionId = "sub_1",
            startsAt = "2025-05-01T10:00:00Z".toInstant(),
            endsAt = "2025-06-01T10:00:00Z".toInstant(),
            subscriptionCancelAt = "2025-06-01T10:00:00Z".toInstant(),
            status = SubscriptionStatus.ACTIVE,
        )

        every {
            sendEmailService.sendEmailDiscordCancelled(
                studentId = 1.toUUID(),
                endsAt = "2025-06-01T10:00:00Z".toInstant(),
            )
        } just Runs

        // when
        underTest.handle(
            ProcessSubscriptionCancelIntentCommand(
                subscriptionIdentifier = "sub_1",
                customerIdentifier = "cus_1",
                productIdentifier = "prod_DISCORD",
            ),
        )

        // then
        verify {
            stripeService.getSubscriptionBySubscriptionId("sub_1")
            sendEmailService.sendEmailDiscordCancelled(
                studentId = 1.toUUID(),
                endsAt = "2025-06-01T10:00:00Z".toInstant(),
            )
        }
    }

    @Test
    fun `process subscription cancel intent - should throw if cancelAt is null`() {
        // given
        dataHelper.getAppUser(
            id = 1.toUUID(),
            userRole = UserRole.STUDENT,
            stripeIdentifier = "cus_1",
            hubspotIdentifier = 1,
        ).also {
            dataHelper.getStudent(
                id = 1.toUUID(),
                studentTier = StudentTier.MASTERCLASS,
                entityModifier = {
                    it.activateDiscordSubscription("2025-02-01T23:59:00Z".toInstant())
                },
            )
        }

        every {
            stripeService.getSubscriptionBySubscriptionId("sub_1")
        } returns GetSubscriptionDataPort.SubscriptionData(
            subscriptionId = "sub_1",
            startsAt = "2025-05-01T10:00:00Z".toInstant(),
            endsAt = "2025-06-01T10:00:00Z".toInstant(),
            subscriptionCancelAt = null,
            status = SubscriptionStatus.ACTIVE,
        )

        // when/then
        shouldThrow<IllegalArgumentException> {
            underTest.handle(
                ProcessSubscriptionCancelIntentCommand(
                    subscriptionIdentifier = "sub_1",
                    productIdentifier = "prod_DISCORD",
                    customerIdentifier = "cus_1",
                ),
            )
        }

        verify {
            stripeService.getSubscriptionBySubscriptionId("sub_1")
        }

        verify(exactly = 0) {
            sendEmailService.sendEmailDiscordCancelled(any(), any())
        }
    }
}
