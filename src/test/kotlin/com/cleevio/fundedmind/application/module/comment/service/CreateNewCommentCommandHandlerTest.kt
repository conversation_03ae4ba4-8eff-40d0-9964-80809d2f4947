package com.cleevio.fundedmind.application.module.comment.service

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.application.module.comment.command.CreateNewCommentCommand
import com.cleevio.fundedmind.domain.comment.CommentRepository
import com.cleevio.fundedmind.domain.user.appuser.constant.UserRole
import com.cleevio.fundedmind.toUUID
import io.kotest.matchers.shouldBe
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.verify
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired

class CreateNewCommentCommandHandlerTest(
    @Autowired private val underTest: CreateNewCommentCommandHandler,
    @Autowired private val commentRepository: CommentRepository,
) : IntegrationTest() {

    @Test
    fun `should create new comment for existing lesson`() {
        // given
        dataHelper.getTrader(
            id = 0.toUUID(),
            firstName = "John",
            lastName = "Doe",
        ).also { trader ->
            dataHelper.getCourse(
                id = 1.toUUID(),
                traderId = trader.id,
                title = "Course",
            ).also { course ->
                dataHelper.getCourseModule(
                    id = 2.toUUID(),
                    courseId = course.id,
                    title = "Module",
                ).also { courseModule ->
                    dataHelper.getLesson(
                        id = 3.toUUID(),
                        courseModuleId = courseModule.id,
                        title = "Lesson",
                    )
                }
            }
        }

        dataHelper.getAppUser(id = 9.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(id = it.id, firstName = "Karol", lastName = "Duchon")
        }

        every {
            zapierService.newCommentUnderLesson(
                commentId = any(),
                commentText = any(),
                commentOwnerName = any(),
                threadId = any(),
                lessonId = any(),
                lessonName = any(),
                courseModuleId = any(),
                courseModuleName = any(),
                courseId = any(),
                courseName = any(),
            )
        } just Runs

        // when
        val result = underTest.handle(
            CreateNewCommentCommand(
                appUserId = 9.toUUID(),
                lessonId = 3.toUUID(),
                text = "Comment text",
                threadId = null,
            ),
        )

        // then
        commentRepository.getReferenceById(result.id).run {
            appUserId shouldBe 9.toUUID()
            text shouldBe "Comment text"
            lessonId shouldBe 3.toUUID()
            threadId shouldBe null
        }

        verify {
            zapierService.newCommentUnderLesson(
                commentId = result.id,
                commentText = "Comment text",
                commentOwnerName = "Karol Duchon",
                threadId = null,
                lessonId = 3.toUUID(),
                lessonName = "Lesson",
                courseModuleId = 2.toUUID(),
                courseModuleName = "Module",
                courseId = 1.toUUID(),
                courseName = "Course",
            )
        }
    }
}
