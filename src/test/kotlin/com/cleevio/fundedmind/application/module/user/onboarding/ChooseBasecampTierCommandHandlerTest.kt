package com.cleevio.fundedmind.application.module.user.onboarding

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.application.module.user.onboarding.command.ChooseBasecampTierCommand
import com.cleevio.fundedmind.application.module.user.student.exception.StudentAlreadyOnboardedException
import com.cleevio.fundedmind.domain.common.constant.OnboardingTier
import com.cleevio.fundedmind.domain.user.onboarding.OnboardingRepository
import com.cleevio.fundedmind.toUUID
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.matchers.shouldBe
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.verify
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.repository.findByIdOrNull

class ChooseBasecampTierCommandHandlerTest(
    @Autowired private val underTest: ChooseBasecampTierCommandHandler,
    @Autowired private val onboardingRepository: OnboardingRepository,
) : IntegrationTest() {

    @Test
    fun `should upgrade onboarding tier to BASECAMP`() {
        dataHelper.getAppUser(id = 1.toUUID(), hubspotIdentifier = 1)
        dataHelper.getOnboarding(1.toUUID())

        every { hubspotService.updateCrmTier(hubspotIdentifier = 1, onboardingTier = any()) } just Runs

        underTest.handle(ChooseBasecampTierCommand(1.toUUID()))

        onboardingRepository.findByIdOrNull(1.toUUID())!!.onboardingTier shouldBe OnboardingTier.BASECAMP

        verify {
            hubspotService.updateCrmTier(hubspotIdentifier = 1, onboardingTier = OnboardingTier.BASECAMP)
        }
    }

    @Test
    fun `should throw if student already onboarded`() {
        dataHelper.getAppUser(id = 1.toUUID())
        dataHelper.getOnboarding(1.toUUID())
        dataHelper.getStudent(1.toUUID())

        shouldThrow<StudentAlreadyOnboardedException> {
            underTest.handle(ChooseBasecampTierCommand(1.toUUID()))
        }
    }
}
