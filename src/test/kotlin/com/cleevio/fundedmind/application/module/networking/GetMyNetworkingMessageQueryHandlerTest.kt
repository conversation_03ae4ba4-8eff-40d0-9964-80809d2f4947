package com.cleevio.fundedmind.application.module.networking

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.application.module.networking.query.GetMyNetworkingMessageQuery
import com.cleevio.fundedmind.domain.common.constant.BadgeColor
import com.cleevio.fundedmind.domain.common.constant.GameLevel
import com.cleevio.fundedmind.domain.common.constant.LevelVisibility
import com.cleevio.fundedmind.domain.file.constant.FileType
import com.cleevio.fundedmind.domain.networking.exception.NetworkingMessageNotAccessibleException
import com.cleevio.fundedmind.domain.user.appuser.constant.UserRole
import com.cleevio.fundedmind.toUUID
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import java.time.Instant
import java.time.temporal.ChronoUnit

class GetMyNetworkingMessageQueryHandlerTest(
    @Autowired private val underTest: GetMyNetworkingMessageQueryHandler,
) : IntegrationTest() {

    @Test
    fun `should return message when user is the recipient and sender is trader`() {
        // given
        // Create sender as trader
        val senderUser = dataHelper.getAppUser(id = 1.toUUID(), userRole = UserRole.TRADER).also {
            dataHelper.getTrader(
                id = it.id,
                firstName = "John",
                lastName = "Doe",
                biography = "Experienced trader",
                position = "Senior Trader",
                badgeColor = BadgeColor.GOLDEN_GRADIENT,
                entityModifier = {
                    it.changeProfilePicture(
                        dataHelper.getImage(
                            type = FileType.TRADER_PROFILE_PICTURE,
                            originalFileUrl = "trader-url",
                            compressedFileUrl = "trader-url-comp",
                            blurHash = "456",
                        ).id,
                    )
                },
            )
        }

        // Create recipient as student
        val recipientUser = dataHelper.getAppUser(id = 2.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(id = it.id)
        }

        // Create networking message
        val networkingMessage = dataHelper.getNetworkingMessage(
            senderUserId = senderUser.id,
            recipientUserId = recipientUser.id,
            text = "Hello, this is a test message",
        )

        // when
        val result = underTest.handle(
            GetMyNetworkingMessageQuery(
                recipientUserId = recipientUser.id,
                networkingMessageId = networkingMessage.id,
            ),
        )

        // then
        result.run {
            message shouldBe "Hello, this is a test message"
            recipientUserId shouldBe recipientUser.id

            // Verify all sender properties
            sender.id shouldBe senderUser.id
            sender.userRole shouldBe UserRole.TRADER
            sender.firstName shouldBe "John"
            sender.bio shouldBe "Experienced trader"
            sender.profilePicture shouldNotBe null
            sender.profilePicture!!.run {
                imageOriginalUrl shouldBe "trader-url"
                imageCompressedUrl shouldBe "trader-url-comp"
                imageBlurHash shouldBe "456"
            }
            sender.discordId shouldBe null
            sender.studentGameLevel shouldBe null
            sender.traderLastName shouldBe "Doe"
            sender.traderBadgeColor shouldBe BadgeColor.GOLDEN_GRADIENT
            sender.traderPosition shouldBe "Senior Trader"
        }
    }

    @Test
    fun `should return message when user is the recipient and sender is a student`() {
        // given
        // Create sender as student
        val senderUser = dataHelper.getAppUser(id = 3.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(
                id = it.id,
                firstName = "Jane",
                biography = "Student studying trading",
                gameLevel = GameLevel.FIVE,
                levelVisibility = LevelVisibility.ENABLED,
                entityModifier = {
                    it.activateDiscordSubscription(Instant.now().plus(30, ChronoUnit.DAYS))
                    it.changeProfilePicture(
                        dataHelper.getImage(
                            type = FileType.STUDENT_PROFILE_PICTURE,
                            originalFileUrl = "student-url",
                            compressedFileUrl = "student-url-comp",
                            blurHash = "789",
                        ).id,
                    )
                },
            ).also {
                dataHelper.getStudentDiscord(
                    studentId = it.id,
                    discordId = "student123",
                )
            }
        }

        // Create recipient as trader
        val recipientUser = dataHelper.getAppUser(id = 4.toUUID(), userRole = UserRole.TRADER).also {
            dataHelper.getTrader(id = it.id)
        }

        // Create networking message
        val networkingMessage = dataHelper.getNetworkingMessage(
            senderUserId = senderUser.id,
            recipientUserId = recipientUser.id,
            text = "Hello from student",
        )

        // when
        val result = underTest.handle(
            GetMyNetworkingMessageQuery(
                recipientUserId = recipientUser.id,
                networkingMessageId = networkingMessage.id,
            ),
        )

        // then
        result.run {
            message shouldBe "Hello from student"
            recipientUserId shouldBe recipientUser.id

            // Verify all sender properties
            sender.id shouldBe senderUser.id
            sender.userRole shouldBe UserRole.STUDENT
            sender.firstName shouldBe "Jane"
            sender.bio shouldBe "Student studying trading"
            sender.profilePicture shouldNotBe null
            sender.profilePicture!!.run {
                imageOriginalUrl shouldBe "student-url"
                imageCompressedUrl shouldBe "student-url-comp"
                imageBlurHash shouldBe "789"
            }
            sender.discordId shouldBe "student123"
            sender.studentGameLevel shouldBe GameLevel.FIVE
            sender.traderLastName shouldBe null
            sender.traderBadgeColor shouldBe null
            sender.traderPosition shouldBe null
        }
    }

    @Test
    fun `should throw exception when user is not the recipient`() {
        // given
        // Create sender as trader
        val senderUser = dataHelper.getAppUser(id = 1.toUUID(), userRole = UserRole.TRADER).also {
            dataHelper.getTrader(id = it.id)
        }

        // Create recipient as student
        val recipientUser = dataHelper.getAppUser(id = 2.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(id = it.id)
        }

        // Create wrong user
        val wrongUser = dataHelper.getAppUser(id = 3.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(id = it.id)
        }

        // Create networking message
        val networkingMessage = dataHelper.getNetworkingMessage(
            senderUserId = senderUser.id,
            recipientUserId = recipientUser.id,
            text = "Test message",
        )

        // when & then
        shouldThrow<NetworkingMessageNotAccessibleException> {
            underTest.handle(
                GetMyNetworkingMessageQuery(
                    recipientUserId = wrongUser.id,
                    networkingMessageId = networkingMessage.id,
                ),
            )
        }
    }
}
