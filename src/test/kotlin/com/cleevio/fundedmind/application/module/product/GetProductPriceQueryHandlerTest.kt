package com.cleevio.fundedmind.application.module.product

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.application.common.command.MoneyResult
import com.cleevio.fundedmind.application.common.port.out.GetDefaultProductPricePort
import com.cleevio.fundedmind.application.module.product.exception.ProductNotFoundException
import com.cleevio.fundedmind.application.module.product.query.GetProductPriceQuery
import com.cleevio.fundedmind.domain.common.constant.MonetaryCurrency
import com.cleevio.fundedmind.domain.common.constant.TaxBehaviour
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.matchers.comparables.shouldBeEqualComparingTo
import io.kotest.matchers.shouldBe
import io.mockk.every
import io.mockk.verify
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired

class GetProductPriceQueryHandlerTest(
    @Autowired private val underTest: GetProductPriceQueryHandler,
) : IntegrationTest() {
    @Test
    fun `should get product price from stripe`() {
        every { existsExternalProductPort.existsByIdentifier("prod_123") } returns true
        every { stripeService.getByIdentifier("prod_123") } returns
            GetDefaultProductPricePort.ProductPrice(
                productIdentifier = "prod_123",
                price = MoneyResult(100, MonetaryCurrency.CZK, 21.toBigDecimal(), TaxBehaviour.INCLUSIVE),
            )

        val result = underTest.handle(
            GetProductPriceQuery(
                "prod_123",
            ),
        )

        result.price.run {
            unitAmount shouldBe 100
            currency shouldBe MonetaryCurrency.CZK
            amount shouldBeEqualComparingTo 1.0.toBigDecimal()
        }

        verify {
            existsExternalProductPort.existsByIdentifier("prod_123")
            stripeService.getByIdentifier("prod_123")
        }
    }

    @Test
    fun `should throw if product does not exist`() {
        every { existsExternalProductPort.existsByIdentifier("prod_123") } returns false

        shouldThrow<ProductNotFoundException> {
            underTest.handle(
                GetProductPriceQuery(
                    "prod_123",
                ),
            )
        }

        verify {
            existsExternalProductPort.existsByIdentifier("prod_123")
        }
    }
}
