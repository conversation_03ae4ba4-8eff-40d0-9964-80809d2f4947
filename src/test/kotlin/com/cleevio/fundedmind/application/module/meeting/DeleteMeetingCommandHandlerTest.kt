package com.cleevio.fundedmind.application.module.meeting

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.application.module.meeting.command.DeleteMeetingCommand
import com.cleevio.fundedmind.domain.meeting.MeetingRepository
import com.cleevio.fundedmind.domain.meeting.traderinmeeting.TraderInMeetingRepository
import com.cleevio.fundedmind.domain.user.trader.TraderRepository
import com.cleevio.fundedmind.toUUID
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.repository.findByIdOrNull

class DeleteMeetingCommandHandlerTest(
    @Autowired private val underTest: DeleteMeetingCommandHandler,
    @Autowired private val meetingRepository: MeetingRepository,
    @Autowired private val traderInMeetingRepository: TraderInMeetingRepository,
    @Autowired private val traderRepository: TraderRepository,
) : IntegrationTest() {

    @Test
    fun `should soft delete meeting but make no changes to traders in meeting`() {
        dataHelper.getTrader(id = 1.toUUID())
        dataHelper.getTrader(id = 2.toUUID())

        dataHelper.getMeeting(
            id = 1.toUUID(),
            traderIds = listOf(1.toUUID(), 2.toUUID()),
        )

        underTest.handle(
            DeleteMeetingCommand(
                meetingId = 1.toUUID(),
            ),
        )

        meetingRepository.findByIdOrNull(1.toUUID())!!.isDeleted shouldBe true
        traderInMeetingRepository.findAll().run {
            this.size shouldBe 2
            this.first { it.traderId == 1.toUUID() }.meetingId shouldBe 1.toUUID()
            this.first { it.traderId == 2.toUUID() }.meetingId shouldBe 1.toUUID()
        }
        traderRepository.findAll().size shouldBe 2
    }
}
