# Test Rules

## Overview

This document outlines the rules and patterns for implementing tests in our projects. It serves as a guide for developers to understand how to write effective tests for different components of the system.

## Testing Architecture

Our testing approach follows a multi-layered strategy that aligns with our hexagonal architecture:

1. **Unit Tests**: Test individual components in isolation
   - Domain entities and value objects
   - Domain services
   - Application services
   - Utility classes

2. **Integration Tests**: Test interactions between components
   - Command and query handlers
   - Repository implementations
   - External service clients

3. **End-to-End Tests**: Test the entire application flow
   - API endpoints
   - Complete use cases

### Key Principles

1. **Test Isolation**: Tests should be independent and not affect each other.
2. **Test Readability**: Tests should be easy to understand and maintain.
3. **Test Coverage**: Critical paths and edge cases should be covered.
4. **Test Speed**: Tests should run quickly to provide fast feedback.
5. **Test Reliability**: Tests should produce consistent results.


### Rules
1. **No variables**: If possible, don't use variable. Follow KISS principle
2. **Hardcode values**: All values should be hardcoded
3. **UUID**: For UUID use `number.toUUID()` (e.g. `1.toUUID()`)
4. **Blocks**: Split test to 3 blocks: Given, When, Then
5. **Don't use production logic**: Never use production logic. For loading data from DB use repositories.
6. **underTest**: Class that are currently testes should have name `underTest`
7. **HelpService**: To prepare data into DB use `IntegrationDataTestHelperService`

## Naming Conventions

- **Test Classes**: Named after the class they test with "Test" suffix (e.g., `UserEntityTest`, `CreateUserCommandHandlerTest`)
- **Test Methods**: Named with descriptive phrases that explain the scenario and expected outcome (e.g., `should create user when valid data provided`, `should throw exception when email already exists`)
- **Test Data**: Named with clear, descriptive names that indicate their purpose (e.g., `validUser`, `userWithInvalidEmail`)

## Testing Tools

1. **JUnit 5**: The primary testing framework
2. **Kotest Assertions**: For expressive assertions
3. **Mockk**: For mocking dependencies
4. **Spring Test**: For integration testing with Spring Boot
5. **Testcontainers**: For integration testing with real databases

## Implementation Patterns

### Test Data Helpers Pattern

Test data helpers provide methods for creating test data in integration tests:

```kotlin
@Service
class IntegrationDataTestHelperService(
   private val userRepository: UserRepository,
   private val fileRepository: FileRepository,
) {

   fun getUser(
      id: UUID = UUIDv7.randomUUID(),
      email: String = "user+${id.suffix}@gimee.com",
      entityModifier: (User) -> Unit = {},
   ): User = userRepository.saveAndFlush(
      User.create(
         id = id,
         email = email,
      ).also {
         entityModifier(it)
      },
   )
}

```

### Unit Test Pattern

Unit tests focus on testing a single unit of code in isolation. They typically follow this pattern:

```kotlin
class UserEntityTest {

    @Test
    fun `should disable account when account is active`() {
        // Given
        val user = User.newStudentAccount(
            id = UUID.randomUUID(),
            email = "<EMAIL>",
            firebaseIdentifier = "firebase-123456",
            hubspotIdentifier = 1,
            stripeIdentifier = "cus_123",
        )

        // When
        user.disableAccount()

        // Then
        user.accountActive shouldBe false
    }

    @Test
    fun `should throw exception when disabling already disabled account`() {
        // Given
        val user = User.newStudentAccount(
            id = UUID.randomUUID(),
            email = "<EMAIL>",
            firebaseIdentifier = "firebase-123456",
            hubspotIdentifier = 1,
            stripeIdentifier = "cus_123",
        ).also { it.disableAccount() }

        // Then
        shouldThrow<UserAccountHasWrongActiveStatusException> {
            // When
            user.disableAccount()
        }
    }
}

```

### Integration Test Pattern

Integration tests verify that different components work together correctly. They typically extend a base integration test class that sets up the test environment:

```kotlin
class CreateUserCommandHandlerTest(
    @Autowired private val underTest: CreateUserCommandHandler,
    @Autowired private val userRepository: UserRepository,
) : IntegrationTest() {

    @Test
    fun `should create user when valid data provided`() {
        // Given
        every {
            firebaseService.createUser(email = "<EMAIL>", rawPassword = "123456")
        } returns Result.success(
            FirebaseUserDetail(
                email = "<EMAIL>",
                firebaseIdentifier = "firebase-123456",
            ),
        )

        // When
        val result = underTest.handle(
            CreateUserCommand(
                email = "<EMAIL>",
                rawPassword = "123456",
            ),
        )

        // Then
        userRepository.findByIdOrNull(result.id)!!.run {
            email shouldBe "<EMAIL>"
            firebaseIdentifier shouldBe "firebase-123456"
        }

        verify {
            firebaseService.createUser(email = "<EMAIL>", rawPassword = "123456")
        }
    }
}

```

### Parameterized Test Pattern

Parameterized tests allow testing multiple scenarios with a single test method:

```kotlin
class EmailValidatorTest {

    @ParameterizedTest
    @CsvSource(
        "<EMAIL>, true",
        "invalid-email, false",
        "test@, false",
        "@example.com, false",
        "test@example, true",
    )
    fun `should validate email correctly`(email: String, isValid: Boolean) {
        // Act & Assert
        EmailValidator.isValid(email) shouldBe isValid
    }
}

```

## Examples

### Service Test
```kotlin
class SignUpUserCommandHandlerTest(
    @Autowired private val userRepository: UserRepository,
    @Autowired private val commandBus: CommandBus,
) : IntegrationTest() {

    @Test
    fun `should sign-up new user`() {
        commandBus(
            SignUpUserCommand(
                email = "<EMAIL>",
                referralCode = null,
            ),
        )

        userRepository.existsByEmailIgnoreCase("<EMAIL>") shouldBe true
    }

    @Test
    fun `should return if user already exists`() {
        helperService.getUser(email = "<EMAIL>")

        commandBus(
            SignUpUserCommand(
                email = "<EMAIL>",
                referralCode = null,
            ),
        )
    }
}

```

### Parametrized Service Test
```kotlin
class GetTelegramChatEvaluationJooqTest(
   @Autowired private val underTest: GetTelegramChatEvaluationJooq,
) : IntegrationTest() {

   data class TradeScenario(
      val name: String,
      val buyPriceUsd1: BigDecimal,
      val sellPriceUsd1: BigDecimal,
      val buyPriceUsd2: BigDecimal,
      val sellPriceUsd2: BigDecimal,
      val expectedWinRate: BigDecimal,
      val expectedTotalReturn: BigDecimal,
      val expectedAvgTradeReturn: BigDecimal,
      val expectedProfitFactor: BigDecimal,
   )

    @DisplayName("Should calculate correct evaluation metrics based on different trade scenarios")
    @ParameterizedTest(name = "{index}: {0}")
    @MethodSource("tradeScenarios")
    fun `should calculate evaluation correctly for different scenarios`(scenario: TradeScenario) {
        // given
        helperService.getTelegramChat(
            id = 1.toUUID(),
            externalChatId = 1,
        )

        // 1. evaluation
        helperService.getTelegramMessageEvaluation(
            id = 1.toUUID(),
            messageId = 1,
            externalChatId = 1,
        )
        helperService.getCompletedContentEvaluation(
            id = 1.toUUID(),
            telegramMessageEvaluationId = 1.toUUID(),
        )
        helperService.getPaperTrade(
            id = 1.toUUID(),
            contentEvaluationId = 1.toUUID(),
            tokenAddress = AddressWrapper("4RCqgKZNDSSKgHYMMLuNQX26fng5D6E6bR59vSi8Wewr"),
            buyMarketCap = 10.toBigDecimal(),
            buyTime = "2024-08-30T10:00:00Z".toInstant(),
            buyPriceUsd = scenario.buyPriceUsd1,
            sellMarketCap = 1_000.toBigDecimal(),
            sellPriceUsd = scenario.sellPriceUsd1,
            state = PaperTradeState.SOLD,
        )

        // 2. evaluation
        helperService.getTelegramMessageEvaluation(
            id = 2.toUUID(),
            messageId = 2,
            externalChatId = 1,
        )
        helperService.getCompletedContentEvaluation(
            id = 2.toUUID(),
            telegramMessageEvaluationId = 2.toUUID(),
        )
        helperService.getPaperTrade(
            id = 2.toUUID(),
            contentEvaluationId = 2.toUUID(),
            tokenAddress = AddressWrapper("4RCqgKZNDSSKgHYMMLuNQX26fng5D6E6bR59vSi8Wewr"),
            buyMarketCap = 20.toBigDecimal(),
            buyTime = "2024-08-30T10:00:00Z".toInstant(),
            buyPriceUsd = scenario.buyPriceUsd2,
            sellMarketCap = 1_000.toBigDecimal(),
            sellPriceUsd = scenario.sellPriceUsd2,
            state = PaperTradeState.SOLD,
        )

        // when
        val res = underTest(chatId = 1)

        // then
        res.winRate.toDouble() shouldBe scenario.expectedWinRate.toDouble()
        res.totalReturn.toDouble() shouldBe scenario.expectedTotalReturn.toDouble()
        res.avgTradeReturn.toDouble() shouldBe scenario.expectedAvgTradeReturn.toDouble()
        res.profitFactor.toDouble() shouldBe scenario.expectedProfitFactor.toDouble()
    }

    companion object {
        @JvmStatic
        fun tradeScenarios(): Stream<TradeScenario> = Stream.of(
            // Case 1: Both trades are profitable (original case)
            TradeScenario(
                name = "Both trades profitable",
                buyPriceUsd1 = 10.toBigDecimal(),
                sellPriceUsd1 = 15.toBigDecimal(),
                buyPriceUsd2 = 100.toBigDecimal(),
                sellPriceUsd2 = 120.toBigDecimal(),
                expectedWinRate = 1.toBigDecimal(),
                expectedTotalReturn = 0.23.toBigDecimal(),
                expectedAvgTradeReturn = 12.5.toBigDecimal(),
                expectedProfitFactor = 1.toBigDecimal(),
            ),

            // Case 2: First trade profitable, second trade at loss
            TradeScenario(
                name = "First trade profitable, second at loss",
                buyPriceUsd1 = 10.toBigDecimal(),
                sellPriceUsd1 = 15.toBigDecimal(),
                buyPriceUsd2 = 100.toBigDecimal(),
                sellPriceUsd2 = 90.toBigDecimal(),
                expectedWinRate = 0.5.toBigDecimal(),
                expectedTotalReturn = (-0.05).toBigDecimal(),
                expectedAvgTradeReturn = (-2.5).toBigDecimal(),
                expectedProfitFactor = (-0.5).toBigDecimal(), // (5/10) - 1 = -0.5
            ),

            // Case 3: Both trades at loss
            TradeScenario(
                name = "Both trades at loss",
                buyPriceUsd1 = 10.toBigDecimal(),
                sellPriceUsd1 = 5.toBigDecimal(),
                buyPriceUsd2 = 100.toBigDecimal(),
                sellPriceUsd2 = 80.toBigDecimal(),
                expectedWinRate = 0.toBigDecimal(),
                expectedTotalReturn = (-0.23).toBigDecimal(),
                expectedAvgTradeReturn = (-12.5).toBigDecimal(),
                expectedProfitFactor = (-1).toBigDecimal(), // (0/25) - 1 = -1
            ),

            // Case 4: First trade at loss, second trade profitable
            TradeScenario(
                name = "First trade at loss, second profitable",
                buyPriceUsd1 = 10.toBigDecimal(),
                sellPriceUsd1 = 5.toBigDecimal(),
                buyPriceUsd2 = 100.toBigDecimal(),
                sellPriceUsd2 = 130.toBigDecimal(),
                expectedWinRate = 0.5.toBigDecimal(),
                expectedTotalReturn = 0.23.toBigDecimal(),
                expectedAvgTradeReturn = 12.5.toBigDecimal(),
                expectedProfitFactor = 5.toBigDecimal(), // (30/5) - 1 = 5
            ),

            // Case 5: One trade with no change, second trade profitable
            TradeScenario(
                name = "One trade with no change, second profitable",
                buyPriceUsd1 = 10.toBigDecimal(),
                sellPriceUsd1 = 10.toBigDecimal(),
                buyPriceUsd2 = 100.toBigDecimal(),
                sellPriceUsd2 = 120.toBigDecimal(),
                expectedWinRate = 0.5.toBigDecimal(),
                expectedTotalReturn = 0.18.toBigDecimal(),
                expectedAvgTradeReturn = 10.toBigDecimal(),
                expectedProfitFactor = 1.toBigDecimal(), // (no losses)
            ),
        )
    }
}

```

### Domain Entity Test

```kotlin
class UserTest {

    @Test
    fun `should create user with correct initial state`() {
        // When
        val user = User.newStudentAccount(
            id = UUID.randomUUID(),
            email = "<EMAIL>",
            firebaseIdentifier = "firebase-123456",
            hubspotIdentifier = 1,
            stripeIdentifier = "cus_123",
        )

        // Then
        user.run {
            email shouldBe "<EMAIL>"
            role shouldBe UserRole.STUDENT
            accountActive shouldBe true
            firebaseIdentifier shouldBe "firebase-123456"
            hubspotIdentifier shouldBe 1
            stripeIdentifier shouldBe "cus_123"
        }
    }

    @Test
    fun `should disable account when account is active`() {
        // Given
        val user = User.newStudentAccount(
            id = UUID.randomUUID(),
            email = "<EMAIL>",
            firebaseIdentifier = "firebase-123456",
            hubspotIdentifier = 1,
            stripeIdentifier = "cus_123",
        )

        // When
        user.disableAccount()

        // Then
        user.accountActive shouldBe false
    }

    @Test
    fun `should throw exception when disabling already disabled account`() {
        // When
        val user = User.newStudentAccount(
            id = UUID.randomUUID(),
            email = "<EMAIL>",
            firebaseIdentifier = "firebase-123456",
            hubspotIdentifier = 1,
            stripeIdentifier = "cus_123",
            entityModifier = { it.disableAccount() }
        )

        // Then
        shouldThrow<UserAccountHasWrongActiveStatusException> {
            // When
            user.disableAccount()
        }
    }

    @Test
    fun `should enable account when account is disabled`() {
        // Given
        val user = User.newStudentAccount(
            id = UUID.randomUUID(),
            email = "<EMAIL>",
            firebaseIdentifier = "firebase-123456",
            hubspotIdentifier = 1,
            stripeIdentifier = "cus_123",
            entityModifier = { it.disableAccount() }
        )

        // When
        user.enableAccount()

        // Then
        user.accountActive shouldBe true
    }
}

```
